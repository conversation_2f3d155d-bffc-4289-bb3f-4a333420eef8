import { Component, ViewChild, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { Client, Contact, SaveFilter, ISavedFilterList, QueryFilter, IFilter, QueryFilterParams } from '../client.model';
import { Table } from 'primeng/table';
import { MessageService, TableState } from 'primeng/api';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { ClientService } from '../client.service';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';
import { FormControl } from '@angular/forms';
import { Debounce } from '@shared/decorators/debounce.decorator';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { AppConstants } from '@shared/constants/app.constant';
import * as _ from 'lodash';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ProjectService } from '@entities/project/project.service';
import { GlobalDetailSubCategory, GlobalDetailTaggingCategory, GlobalDetailTags, SubCategory, TagCategory } from '@entities/administration/administration.model';
import { TreeViewStructure, TreeNode, PNGTree } from '@entities/administration/append-tags/tree-view-model';
import { AdministrationService } from '@entities/administration/administration.service';
import { Input } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AppendTagsComponent } from '@entities/administration/append-tags/append-tags.component';
import { ColumnToggleService } from '@shared/services/column-toggle.service';
import { UtilizationService } from '@entities/utilization-management/utilization.service';
import { ComponentsType, FiledType } from '@shared/models/component-type-enum';
import { ExtendedFormComponent } from '@shared/components/extended-form/extended-form.component';
import { extendedField } from '@shared/models/extended-field.model';
import { LayoutConfigService } from '@shared/services/layout-config.service';

@Component({
  selector: 'app-manage-client',
  templateUrl: './manage-client.component.html',
  styleUrls: ['./manage-client.component.scss'],
  providers: [MessageService],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageClientComponent extends SflBaseComponent implements OnInit {
  listSelected = false;
  cardTitle = 'Client';
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save btn-add-wrapper',
      btnText: 'Add New',
      title: 'Create New Client',
      redirectPath: this.appRoutes.CREATE_CLIENT,
      permissions: [this.permissionModules.MANAGE_CLIENT]
    },
    {
      btnSvg: 'download-wt',
      btnClass: 'btn-filter-icon download',
      action: this.openExportOptionList.bind(this)
    }
  ];
  showFilter = false;
  splitButtonDropDownOption = {
    action: this.showFilterOption.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.showSavedFilterDropDown();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSave();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilters();
        }
      }
    ]
  };
  customers: Client[] = [];
  sortColumnFlag = false;
  pageChangeFlag = false;
  statuses: any[];
  sortFieldName: string = 'name';
  sortOrderNumber: number = 1;
  loading = false;
  totalRecords: number;

  @ViewChild('dt') table: Table;
  dataFilter: IFilter = new IFilter();

  availableFilters: ISavedFilterList;
  filteredFilters: ISavedFilterList;
  sharedFilters: QueryFilter[] = [];
  myFilters: QueryFilter[] = [];
  selectedFilter: QueryFilter;
  showSavedFilter = false;
  selectedFilterFormControl = new FormControl('');
  showPaginator: boolean;
  filteredFlag = false;
  showEditDialog = false;
  editFilterObj: QueryFilter;
  showNameError = false;
  showDeleteDialog = false;
  deleteFilterObj: QueryFilter;
  showShareDialog = false;
  shareFilterObj = null;
  queryFilterId: number;
  positionEdit = false;
  frozenCols = [];
  _pCols: string[] = [];
  isShowHideColumns: boolean = false;
  _selectedColumns: any;
  selectedColumnsArray: any = [];
  draggedColumnIndex: any;
  showTagDialog = false;
  selectedTagToView;
  selectedTags = [];
  treeViewSelectedTags = [];
  finalTagsAlongWithTheCategory: string[] = [];
  groupedCategory: TreeViewStructure;
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  tagSubCategory: SubCategory[] = [];
  globalDetailsTagSubCategory: GlobalDetailSubCategory;
  globalDetailsTag: GlobalDetailTags;
  taggingTags = [];
  tagCategories: TagCategory[] = [];
  selectedCategoriesTag: PNGTree[] = [];
  checkedClient: Client[];
  extendFields: any;
  globalDetailId: any;
  componentType = ComponentsType.Client;
  filedType = FiledType;
  showUpdateExtendFiledDialog = false;
  updateExtendFiled = '';
  clientObj: Client;
  clientId: number = 0;
  clientSetupForm: Client;
  UPDATE = AppConstants.UPDATE;
  extendFiledFilter: any = {};
  showHyperlinkNavigationDialog = false;
  linkValue = '';
  fieldDetail: extendedField;
  exportReportData = [];
  excelExportReportData = [];
  clientExportData = [];
  exportPdfColumns = [];
  excelHeaders = [];
  csvCols = [];
  showExportOptions: boolean = false;
  showExportOptionDialog: boolean;

  @Input() get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any) {
    setTimeout(() => {
      this.columnToggle.setSelectedColumns(this._selectedColumns, 'client');
      const col = this._selectedColumns;
      if (col) {
        this._selectedColumns = col.filter((val) => val.includes(val));
      } else {
        this._selectedColumns = this.frozenCols.filter((col) => val.includes(col));
      }
      this._pCols = col?.map((f) => f.field);
    }, 500);
  }

  @ViewChild('childForm') extendedFieldFormComponent: ExtendedFormComponent;

  constructor(
    private readonly clientService: ClientService,
    private readonly cdf: ChangeDetectorRef,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly layoutConfigService: LayoutConfigService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly projectService: ProjectService,
    private readonly administrationService: AdministrationService,
    private readonly dialog: MatDialog,
    private readonly columnToggle: ColumnToggleService,
    private readonly utilizationService: UtilizationService
  ) {
    super();
  }
  ngOnInit() {
    // replace this with enum
    this.getGlobalDetailsCategory();
    this.checkEmployeeFilterStatus(this.appConstants.MANAGE_SCREENS.CLIENT);
    this.statuses = [
      { label: 'All', value: '' },
      { label: 'Active', value: 'true' },
      { label: 'Inactive', value: 'false' }
    ];
    this.loading = true;
    this.getStoredFilters();
    this.getCategoryMasterData();
    if (this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.CLIENT)) {
      this.dataFilter = this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.CLIENT);
    }
    this.frozenCols = [
      { field: 'company', monthLabel: 'Company', sort: true },
      { field: 'primary_contact', monthLabel: 'Primary Contact' },
      { field: 'phone', monthLabel: 'Phone' },
      { field: 'email', monthLabel: 'Email' },
      { field: 'status', monthLabel: 'Status' },
      { field: 'tags', monthLabel: 'Tags' }
    ];

    this._selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.client
      ? JSON.parse(localStorage.getItem('selectedColumnsArray'))[this.appConstants.MANAGE_SCREENS.CLIENT]
      : this.frozenCols;
    this._pCols = this._selectedColumns.map((f) => f.field);
    this.clientService.showNewTags.subscribe((res) => {
      this.filteredFlag = true;
      this.loading = true;
      this.customers = [];
      if (res === true) {
        this.loadClient(this.table);
        this.checkedClient = [];
      }
    });
  }

  addTagsToMultipleClient() {
    const customerId: number[] = [];
    for (const customer of this.checkedClient) {
      customerId.push(customer.customer.id);
    }
    const dialogRef = this.dialog.open(AppendTagsComponent, {
      data: {
        tags: [],
        employeeId: customerId,
        title: 'Add Tag',
        flag: 'ADD TAGS TO CHECKED CLIENT'
      },
      width: '880px'
    });
    dialogRef.afterClosed().subscribe((res) => {
      if (res) {
        this.listSelected = false;
        this.checkedClient = [];
        this.cdf.detectChanges();
      }
    });
  }

  getCategoryMasterData() {
    this.tagCategories = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.administrationService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.tagCategories = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.administrationService.setTagCategories(globalDetail[0].global_detail);
              this.getTagSubCategories();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getGlobalDetailTags() {
    this.taggingTags = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.administrationService.getTags('TagManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
              this.globalDetailsTag = globalDetail[0];
              this.taggingTags = globalDetail[0].global_detail.extended_fields.tags;
              this.administrationService.setTags(globalDetail[0].global_detail);
              this.combineCategoryAndSubCategory();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  initGroupingCategoryTags() {
    this.groupedCategory = { data: [] };
    for (const [index, category] of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory.entries()) {
      const dataCollection: TreeNode = new TreeNode();
      dataCollection.label = category.name;
      dataCollection.selectable = false;
      dataCollection.collapsedIcon = 'pi-chevron-right';
      dataCollection.expandedIcon = 'pi-chevron-down';
      dataCollection.expanded = true;
      for (const [subIndex, subCate] of category?.subTagCategory?.entries()) {
        dataCollection.children.push({
          label: subCate.name,
          collapsedIcon: 'pi-chevron-right',
          expandedIcon: 'pi-chevron-down',
          children: [],
          selectable: false,
          expanded: true
        });
        if (subCate?.tags?.length) {
          for (const [tagIndex, tag] of subCate?.tags?.entries()) {
            dataCollection.children[subIndex]?.children?.push({
              label: tag.name,
              collapsedIcon: 'pi-chevron-right',
              expandedIcon: 'pi-chevron-down',
              expanded: true
            });
          }
        }
      }
      for (const [tagIndex, tag] of category?.tags?.entries()) {
        dataCollection.children?.push({
          label: tag.name,
          collapsedIcon: 'pi-chevron-right',
          expandedIcon: 'pi-chevron-down',
          expanded: true
        });
      }
      this.groupedCategory.data.push(dataCollection);
    }
    this.onFilterChangePreapareSelectedTreeNodes();
  }

  getTagSubCategories() {
    this.tagSubCategory = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.administrationService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTagSubCategory = globalDetail[0];
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
              this.administrationService.setTagSubCategories(globalDetail[0].global_detail);
              this.getGlobalDetailTags();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  combineCategoryAndSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
    this.injectTagsToRespectiveCategoryOrSubCategory();
    this.initGroupingCategoryTags();
  }

  injectTagsToRespectiveCategoryOrSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      for (const tag of this.taggingTags) {
        if (tag.tagCategory === category.id) {
          const subCateIndex = category.subTagCategory.findIndex((subCate) => subCate.id === tag.subTagCategory);
          if (subCateIndex !== -1) {
            category.subTagCategory[subCateIndex]['tags'].push(tag);
          } else {
            category.tags.push(tag);
          }
        }
      }
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
  }

  tagSelected(event) {
    this.treeViewSelectedTags = [];
    this.dataFilter.tags = this.finalizedTags.length > 1 ? this.finalizedTags.split(',').toString() : this.finalizedTags.toString();
    this.filter();
  }

  get finalizedTags(): string {
    this.finalTagsAlongWithTheCategory = [];
    const categoryAndTags = this.selectedTags;
    this.selectedCategoriesTag = categoryAndTags.filter((tag) => !tag.hasOwnProperty('selectable'));
    for (const selectedTags of this.selectedCategoriesTag) {
      let labelHolder = '';
      labelHolder += selectedTags.label;
      if (selectedTags.parent) {
        labelHolder = selectedTags.parent.label + '__' + labelHolder;
        if (selectedTags.parent?.parent) {
          labelHolder = 'equals:' + selectedTags.parent?.parent?.label + '__' + labelHolder;
          if (selectedTags.parent?.parent?.parent) {
            labelHolder = selectedTags.parent?.parent?.parent?.label + '__' + labelHolder;
          }
        }
      }
      this.finalTagsAlongWithTheCategory.push(labelHolder);
    }
    return this.finalTagsAlongWithTheCategory.toString();
  }

  getExtractedTagsParentCategory(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 2];
  }

  onFilterChangePreapareSelectedTreeNodes() {
    if (this.dataFilter.tags?.length) {
      this.selectedTags = [];
      const tagsWithCategory = this.dataFilter.tags.split(',');
      for (const tag of tagsWithCategory) {
        const pngTreeItem: PNGTree = new PNGTree();
        const tagLength = tag.split('__');
        pngTreeItem.collapsedIcon = 'pi-chevron-right';
        pngTreeItem.expandedIcon = 'pi-chevron-down';
        pngTreeItem.label = this.getExtractedTags(tag);
        pngTreeItem.expanded = true;
        pngTreeItem.parent = {
          label: this.getExtractedTagsParentCategory(tag),
          children: [],
          collapsedIcon: 'pi-chevron-right',
          expandedIcon: 'pi-chevron-down',
          expanded: true,
          parent:
            tagLength?.length > 2
              ? {
                  label: this.getExtractedTagsParentCategory(tag),
                  children: [],
                  collapsedIcon: 'pi-chevron-right',
                  expandedIcon: 'pi-chevron-down',
                  expanded: true,
                  parent: undefined
                }
              : undefined
        };
        if (this.groupedCategory) {
          for (const parent of this.groupedCategory.data) {
            for (const children of parent.children) {
              for (const children_data of children.children) {
                if (children_data.label === pngTreeItem.label) {
                  this.selectedTags.push(children_data);
                }
              }
            }
          }
        }
      }
      this.cdf.detectChanges();
    }
  }

  clearCompanyFilter() {
    delete this.dataFilter.name_search;
    this.filter();
  }

  pageChange() {
    this.loading = true;
    this.customers = [];
    this.pageChangeFlag = true;
    this.updateEmployeeFilterStatus(this.appConstants.MANAGE_SCREENS.CLIENT);
  }

  sortColumn() {
    this.sortColumnFlag = true;
  }

  showHideColumns(type) {
    if (type == 'showColumns') {
      this.isShowHideColumns = true;
    } else {
      this.isShowHideColumns = false;
    }
  }

  onSelectColumsChange(event) {
    if (event) {
      this.columnToggle.setSelectedColumns(event.value, 'client');
      this._selectedColumns = event.value;
      this._pCols = event.value.map((f) => f.field);
      this.prepareHeaders();
    }
    this.isShowHideColumns = !this.isShowHideColumns;
  }

  getTagCategorySubCategory(tags: string): string {
    const tagArray = tags.split('__');
    const categoryName = tagArray[0];
    const subCategory = tagArray?.length > 2 ? tagArray[1] : null;
    if (subCategory) return `Category <strong>${categoryName}</strong> <br> Sub Category<strong>${subCategory}</strong>`;
    else return `Category <strong>${categoryName}</strong>`;
  }

  toggleWithCategory(tooltip, tag) {
    if (tooltip.isOpen()) {
      tooltip.close();
    } else {
      tooltip.open({ tag });
    }
  }

  openTagModal(tag) {
    this.showTagDialog = true;
    this.selectedTagToView = tag;
  }

  getTagsCount(tagWithCategory: string, allowedTagLength?: boolean): string {
    if (allowedTagLength) {
      const tagArray = tagWithCategory.split('__');
      return tagArray[tagArray.length - 1];
    }
  }

  getTagCount(tags: string[]): string {
    return '+ ' + (tags?.length - 2);
  }

  getExtractedTags(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 1];
  }

  // used to apply filter on the table
  filter(): void {
    if (this.dataFilter.is_active == 'null') {
      delete this.dataFilter.is_active;
    }
    if (this.dataFilter?.order_by) {
      this.dataFilter.order_by = this.dataFilter.order_by.replace(/%3A/g, ':');
    }
    this.filteredFlag = true;
    this.loading = true;
    this.customers = [];
    this.dataFilter.offset = this.appConstants.DEFAULT_PAGE;
    this.dataFilter.limit = this.table._rows;
    this.pageChangeFlag = false;
    this.loadClient(this.table);
  }

  // used to convert and object into query param string
  applyFilter() {
    this.showSavedFilter = false;
    this.dataFilter = JSON.parse('{"' + decodeURI(this.selectedFilter?.query_filter?.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    this.filter();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters.data.query_filters;
    if (this.dataFilter?.tags) {
      this.dataFilter.tags = this.dataFilter.tags.replace(/%3A/g, ':').replace(/%2C/g, ',');
    }
  }

  showSavedFilterDropDown() {
    this.showSavedFilter = !this.showSavedFilter;
  }

  showFilterOption() {
    this.showFilter = !this.showFilter;
  }

  @Debounce()
  loadClient(event?: TableState) {
    let queryFilter: QueryFilterParams = {
      limit: this.pageChangeFlag ? (event?.rows ? event?.rows : this.dataFilter?.limit) : this.dataFilter?.limit ? this.dataFilter.limit : this.appConstants.DEFAULT_ROWS_PER_PAGE,
      offset: this.pageChangeFlag
        ? event?.first !== undefined && event?.first !== null
          ? event?.first
          : this.dataFilter?.offset
        : this.dataFilter?.offset
          ? this.dataFilter.offset
          : this.appConstants.DEFAULT_PAGE,
      order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort(event)) : this.activeSort(event)
    };
    if (!this.pageChangeFlag) {
      this.table._first = this.dataFilter?.offset ? this.dataFilter.offset : event?.first;
      this.table._rows = this.dataFilter?.limit ? this.dataFilter.limit : event?.rows;
    }
    if (!this.sortColumnFlag) {
      this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : event?.sortField;
      this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by.split(':')[0] === 'asc' ? 1 : -1) : event?.sortOrder;
    }
    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter, ...queryFilter }, this.appConstants.MANAGE_SCREENS.CLIENT);
      if (this.filteredFlag) {
        queryFilter.offset = 0;
        event.first = 0;
        this.filteredFlag = false;
      }
      for (const [key] of Object.entries(this.dataFilter)) {
        if (key !== 'offset' && key !== 'limit' && key !== 'order_by') queryFilter[`${key}`] = this.dataFilter[key];
      }
    }
    queryFilter = { ...queryFilter, ...this.extendFiledFilter };
    queryFilter = this.queryStringUtil(queryFilter);

    this.subscriptionManager.add(
      this.clientService.getClientData(queryFilter).subscribe(
        (res) => {
          this.loading = false;
          this.customers = res.body.data.customers;
          this.totalRecords = Number(res.headers.get('x-total-count'));
          this.showPaginator = this.totalRecords <= 10 ? false : true;
          this.getCustomerDataForExport(queryFilter);
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
  }

  getCustomerDataForExport(queryFilter: QueryFilterParams) {
    if (queryFilter['limit']) {
      delete queryFilter['limit'];
    }
    if (queryFilter['offset'] != null) {
      delete queryFilter['offset'];
    }

    this.subscriptionManager.add(
      this.clientService.getClientData(queryFilter).subscribe((res) => {
        if (res?.body?.data?.customers?.length) {
          this.clientExportData = res.body.data.customers;
          this.prepareHeaders();
        }
      })
    );
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
      }
    }
    return queryStringParam;
  }

  activeSort(event: TableState) {
    if (event.sortField) {
      if (event.sortOrder === 1) {
        return 'asc:' + event.sortField;
      } else {
        return 'desc:' + event.sortField;
      }
    }
    return null;
  }
  onActivityChange(event) {
    const value = event.target.value;
    if (value && value.trim().length) {
      const activity = parseInt(value);

      if (!isNaN(activity)) {
        this.table.filter(activity, 'activity', 'gte');
      }
    }
  }

  // saving the filter, prompting the user to provide the filter group name.
  onSave() {
    const requestObject: SaveFilter = {
      query_string: this.serialize(this.dataFilter),
      resource: 'customers'
    };
    const dialogTitle = 'Save Client Filter';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject);
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        this.getStoredFilters();
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }

  // converts the object in to query param string
  serialize = (obj) => {
    const str = [];
    for (const p in obj) {
      if (obj.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
      }
    }
    return str.join('&');
  };

  getStoredFilters() {
    const requestObject = {
      resource: 'customers'
    };
    this.subscriptionManager.add(
      this.clientService.getStoredFilters(requestObject).subscribe(
        (res: ISavedFilterList) => {
          this.loading = false;
          this.sharedFilters = [];
          this.myFilters = [];
          this.availableFilters = res;
          this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
          this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
          this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
          this.cdf.detectChanges();
          this.routerListener();
        },
        () => (this.loading = false)
      )
    );
  }

  getContactInfo(contactInfo: Contact[], key: string) {
    if (contactInfo?.length > 0) {
      const firstRecord = contactInfo.find((info) => info.contact.is_primary === true);
      if (firstRecord) {
        switch (key) {
          case 'name':
            return firstRecord.contact.name;
          case 'email':
            return firstRecord.contact.email;
          case 'phone':
            return firstRecord.contact.phone;
          default:
            return '-';
        }
      }
    } else {
      return '-';
    }
  }

  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter.length
      ? this.availableFilters.data.query_filters.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters.data.query_filters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === false);
  }

  resetFilters(): void {
    this.dataFilter = new IFilter();
    this.cacheFilter.resetCacheFilters(this.appConstants.MANAGE_SCREENS.CLIENT);
    // reseting the url by removing the filter query string
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: null },
      queryParamsHandling: 'merge'
    });
    // reset the filter selection as well
    this.selectedFilterFormControl = new FormControl('');
    this.dataFilter.tags = '';
    this.selectedTags = [];
    this.pageChangeFlag = false;
    this.dataFilter.limit = this.appConstants.DEFAULT_ROWS_PER_PAGE;
    this.dataFilter.offset = this.appConstants.DEFAULT_PAGE;

    if (this.table) {
      this.table.first = this.appConstants.DEFAULT_PAGE;
      this.table.rows = this.appConstants.DEFAULT_ROWS_PER_PAGE;
    }
    this.filter();
  }

  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }

  closeModal() {
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.clientService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          (res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.cdf.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  inputFilterName() {
    this.showNameError = false;
  }

  deleteFilter(filterOption) {
    this.showDeleteDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.clientService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.utilizationService.showNewSharedFilter.next('Manage Clients');
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Share',
      text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Unshare',
      text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.clientService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.utilizationService.showNewSharedFilter.next('Manage Clients');
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      }
    });
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            const filterValue = this.sharedFilters?.find((f) => JSON.stringify(f) === JSON.stringify(this.selectedFilter));
            if (filterValue) {
              this.selectedFilterFormControl.setValue(filterValue);
            }
            this.applyFilter();
            this.onFilterChangePreapareSelectedTreeNodes();
          }
        },
        (error) => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  applySelectedFilterAndUpdateUrl() {
    this.showSavedFilter = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: this.selectedFilter?.query_filter?.id },
      queryParamsHandling: 'merge'
    });
  }

  listChecked(): void {
    this.checkedClient.length ? (this.listSelected = true) : (this.listSelected = false);
  }

  removeClient(): void {
    this.checkedClient = [];
  }

  selectAllClientCheck(): void {
    this.checkedClient = this.customers;
  }

  updateEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    this.cacheFilter.setCacheFilters({ ...filter, ...{ filterOpen: this.showFilter } }, page);
  }

  checkEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    if (filter?.filterOpen) {
      this.showFilter = filter.filterOpen;
    }
  }

  getValueByPartialKey(dbTag: string, extendFieldsObj: any = {}, fieldType: string = ''): string {
    if (fieldType === this.filedType.MultiDropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      const value = Array.isArray(extendFieldsObj[dbTag]) ? extendFieldsObj[dbTag]?.map((item) => item?.name).join(', ') : '';
      return value;
    }
    if (fieldType === this.filedType.Dropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag].name : '';
    }
    return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag] : '';
  }

  getGlobalDetailsCategory(): void {
    this.subscriptionManager.add(
      this.administrationService.getExtendedFields('ManageExtendedFiled').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'ManageExtendedFiled') {
              this.extendFields = res.data.global_details[0].global_detail.extended_fields.extendArray || [];
              this.globalDetailId = res.data.global_details[0].global_detail.id;

              this.frozenCols = [...this.frozenCols, ...this.extractExtendFlow()];
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  checkSelectedColumn(key: string): boolean {
    const finalKey = key?.replace(/\s/g, '_')?.trim() || '';
    return this._pCols.includes(finalKey);
  }

  extractNames(data: any, componentName: string): string[] {
    return data
      ?.map((item) => {
        const projectConfig = item?.jsonData?.extendedFieldsConfig?.find((config) => config?.component === componentName);
        return projectConfig ? projectConfig?.fields.map((field) => field?.name?.trim()) : [];
      })
      .flat(2);
  }

  extractExtendFlow(): Array<any> {
    let extendFiled = [];
    let prepareKey = this.extractNames(this.extendFields, this.componentType);
    for (const key of prepareKey) {
      extendFiled.push({ field: key?.replace(/\s/g, '_')?.trim() || '', monthLabel: key?.toUpperCase() });
    }
    return extendFiled;
  }

  openExtendFiledPopup(clientObj: Client, fieldDetail: extendedField, isEditLink = false): void {
    this.fieldDetail = fieldDetail;
    this.linkValue = this.getValueByPartialKey(fieldDetail?.DBTag, clientObj?.customer?.extended_fields, fieldDetail?.type) || '';
    if (fieldDetail?.type === this.filedType.Hyperlink && this.linkValue && !isEditLink) {
      this.openHyperlinkPopup(clientObj, fieldDetail?.name);
      return;
    }
    this.updateExtendFiled = fieldDetail?.name;
    this.showUpdateExtendFiledDialog = true;
    this.clientObj = clientObj;
    this.clientId = clientObj.customer.id;
    this.clientSetupForm = JSON.parse(JSON.stringify(this.clientObj));
    this.showHyperlinkNavigationDialog = false;
  }

  openHyperlinkPopup(clientObj: Client, fieldName?: string): void {
    this.showHyperlinkNavigationDialog = true;
    this.updateExtendFiled = fieldName;
    this.clientObj = clientObj;
  }

  closeExtendFiledPopup(): void {
    this.customers = this.customers.map((customer) => {
      if (customer.customer.id === this.clientId) {
        return {
          customer: {
            ...customer.customer,
            extended_fields: { ...this.clientSetupForm.customer.extended_fields }
          }
        };
      }
      return customer;
    });

    this.updateExtendFiled = '';
    this.showUpdateExtendFiledDialog = false;
    this.clientObj = {};
    this.fieldDetail = null;
    this.extendedFieldFormComponent?.onReset();
  }

  saveClient(): void {
    if (JSON.stringify(this?.clientSetupForm.customer?.extended_fields) !== JSON.stringify(this?.clientObj?.customer.extended_fields)) {
      this.loading = true;
      const updatedClientObj = this.processUpdateObj(this.clientObj.customer) as Client;
      this.subscriptionManager.add(
        this.clientService.updateClient(this.clientId, updatedClientObj).subscribe(
          (res) => {
            this.extendedFieldFormComponent?.onReset();
            this.loadClient(this.table);
            this.layoutUtilsService.showActionNotification(AppConstants.updateClient, AlertType.Success);
          },
          (err) => {
            this.loading = false;
            this.layoutUtilsService.showActionNotification(err.Message, AlertType.Error);
          }
        )
      );
    }
    this.closeExtendFiledPopup();
  }

  processUpdateObj(clientSetupForm) {
    return {
      extended_fields: clientSetupForm?.extended_fields || {}
    };
  }

  filterExtendFiled(dbTag: string, event: any): void {
    if (event?.value) {
      this.extendFiledFilter[dbTag] = `${event?.value?.trim()}` as string;
    } else {
      this.deleteExtendFiledFilter(dbTag);
    }
    this.filter();
  }
  deleteExtendFiledFilter(dbTag): void {
    if (this.extendFiledFilter?.hasOwnProperty(dbTag)) {
      delete this.extendFiledFilter[dbTag];
    }
    this.filter();
  }

  getFilterValue(dbTag: string): string {
    return this.extendFiledFilter?.hasOwnProperty(dbTag) ? this.extendFiledFilter[dbTag] : ('' as string);
  }

  getValidLink(link: string): string {
    if (!link) return '';
    return this.appConstants.regexForHyperlink.test(link) ? link : `https://${link}`;
  }

  checkExtendedFormValidity(): boolean {
    const isValid = this.extendedFieldFormComponent?.isFormValid() || false;
    return !isValid;
  }

  navigateOnLink(): void {
    this.showHyperlinkNavigationDialog = false;
    this.fieldDetail = null;
    if (this.linkValue) {
      const hyperLink = this.getValidLink(this.linkValue);
      window.open(hyperLink, '_blank');
      this.linkValue = '';
    }
  }

  openExportOptionList(): void {
    this.showExportOptionDialog = true;
    this.showExportOptions = true;
    this.cdf.detectChanges();
  }

  prepareHeaders(): void {
    this.exportReportData = [];
    this.excelExportReportData = [];
    this.setExportHeaders();
    if (this.clientExportData.length) {
      for (const client of this.clientExportData) {
        let exportData = {};
        let excelExportData = {};
        exportData = {
          company: client?.customer?.name,
          primary_contact: this.getContactInfo(client?.customer?.contacts, 'name'),
          phone: this.getContactInfo(client.customer.contacts, 'phone'),
          email: this.getContactInfo(client.customer.contacts, 'email'),
          status: client.customer.is_active ? 'Active' : 'Inactive',
          tags: client.customer.tags?.length ? client.customer.tags.join(', ') : '-'
        };
        exportData = (this.processObj(client) || []).reduce((acc, obj) => ({ ...acc, ...obj }), exportData);
        excelExportData = { ...exportData };
        this.exportReportData.push(exportData);
        this.excelExportReportData.push(excelExportData);
      }
    }
    this.layoutConfigService.updateHeight$.next(true);
    this.filterExcelDataWithSelectedColumns();
    this.cdf.detectChanges();
  }

  processObj(data: Client) {
    let final = this.extendFields
      ?.map((item) => {
        return item?.jsonData?.extendedFieldsConfig?.map((filed) => {
          return filed?.fields?.map((fieldDetails) => {
            let dateColumns = {};
            const fieldName = `${fieldDetails?.name?.replace(/\s/g, '_')?.trim()}`?.trim();
            if (this._pCols?.includes(fieldName)) {
              dateColumns[fieldName] = this.getValueByPartialKey(fieldDetails?.DBTag, data?.customer?.extended_fields, fieldDetails.type);
            }
            return { ...dateColumns };
          });
        });
      })
      ?.flat();
    return final?.flat();
  }

  setExportHeaders(): void {
    this.exportPdfColumns = this.selectedColumns.map((col) => ({
      title: col.monthLabel,
      dataKey: col.field
    }));

    const fieldOrder = this.frozenCols.map((col) => col.field);
    const matching = [];
    const nonMatching = [];

    for (const item of this.exportPdfColumns) {
      if (fieldOrder.includes(item.dataKey)) {
        matching.push(item);
      } else {
        nonMatching.push(item);
      }
    }

    matching.sort((a, b) => {
      return fieldOrder.indexOf(a.dataKey) - fieldOrder.indexOf(b.dataKey);
    });

    this.exportPdfColumns = [...matching, ...nonMatching];

    this.csvCols = this.exportPdfColumns.map((col) => col.dataKey);

    this.excelHeaders = [
      this.exportPdfColumns?.reduce((acc, item) => {
        acc[item.dataKey] = item.title;
        return acc;
      }, {})
    ];
  }

  filterExcelDataWithSelectedColumns(): void {
    this.excelExportReportData = this.excelExportReportData.map((res) => {
      const filteredData = {};
      for (const key in this.excelHeaders[0]) {
        if (res[key]) {
          filteredData[key] = res[key];
        }
      }
      return filteredData;
    });
  }

  exportReport(type: string): void {
    if (type === 'csv') {
      this.utilizationService.exportToCsv(this.exportReportData, 'manageClient', this.csvCols);
    }
    if (type === 'pdf') {
      this.utilizationService.exportPdf(this.exportPdfColumns, this.exportReportData, 'manageClient', this.selectedColumns.length);
    }
    if (type === 'excel') {
      this.utilizationService.exportExcel(this.excelHeaders, this.excelExportReportData, 'manageClient');
    }
  }

  ngOnDestroy(): void {
    this.updateEmployeeFilterStatus(this.appConstants.MANAGE_SCREENS.CLIENT);
  }
}
