// ========================================
// DECORATOR TUTORIAL - FROM BASICS TO ADVANCED
// ========================================

// What is a Decorator?
// A decorator is a special function that can modify or enhance classes, methods, properties, or parameters
// Think of it like adding a "label" or "instruction" to your code that changes how it behaves

// ========================================
// PART 1: BASIC FUNCTION DECORATOR
// ========================================

// Step 1: Let's start with a simple function that logs when another function is called
function logFunction(originalFunction: Function) {
  // This function receives another function as input
  console.log('Decorator is wrapping the function');

  // Return a new function that does extra work
  return function (...args: any[]) {
    console.log('Before calling the original function');

    // Call the original function with all its arguments
    const result = originalFunction.apply(this, args);

    console.log('After calling the original function');
    return result;
  };
}

// Step 2: Using the decorator manually (without @ syntax)
function sayHello(name: string) {
  console.log(`Hello, ${name}!`);
}

// Manually applying the decorator
const decoratedSayHello = logFunction(sayHello);
decoratedSayHello('John'); // This will show the logging

// ========================================
// PART 2: METHOD DECORATOR WITH @ SYNTAX
// ========================================

// Step 3: Now let's create a proper method decorator
function LogMethod(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  // target: The class that contains the method
  // propertyName: The name of the method being decorated
  // descriptor: An object that describes the method (contains the actual function)

  console.log(`Decorating method: ${propertyName}`);

  // Get the original method
  const originalMethod = descriptor.value;

  // Replace the method with our enhanced version
  descriptor.value = function (...args: any[]) {
    console.log(`Calling method: ${propertyName} with arguments:`, args);

    // Call the original method
    const result = originalMethod.apply(this, args);

    console.log(`Method ${propertyName} finished with result:`, result);
    return result;
  };
}

// Step 4: Using the decorator with @ syntax
class Calculator {
  @LogMethod // This is the decorator syntax
  add(a: number, b: number): number {
    return a + b;
  }

  @LogMethod
  multiply(a: number, b: number): number {
    return a * b;
  }
}

// Test the decorated methods
const calc = new Calculator();
calc.add(5, 3); // Will show logging
calc.multiply(4, 2); // Will show logging

// ========================================
// DETAILED EXPLANATION OF .apply() METHOD
// ========================================

// Let's understand what .apply() does step by step
console.log('\n=== UNDERSTANDING .apply() METHOD ===');

// Example 1: Basic function call vs apply
function greet(greeting, punctuation) {
  return `${greeting}, my name is ${this.name}${punctuation}`;
}

const person1 = { name: 'Alice' };
const person2 = { name: 'Bob' };

// Normal function call (this = undefined or global object)
// greet("Hello", "!"); // This would not work properly

// Using .apply() to set 'this' context
const result1 = greet.apply(person1, ['Hello', '!']);
const result2 = greet.apply(person2, ['Hi', '.']);

console.log('Using apply with person1:', result1);
console.log('Using apply with person2:', result2);

// Example 2: Why we need apply in decorators
class SimpleCalculator {
  constructor(public name: string) {}

  add(a: number, b: number): number {
    console.log(`${this.name} is calculating ${a} + ${b}`);
    return a + b;
  }
}

// Let's see what happens WITHOUT apply
function BadDecorator(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;

  descriptor.value = function (...args: any[]) {
    console.log('Before method call');

    // WRONG WAY - calling without proper 'this' context
    // const result = originalMethod(args[0], args[1]); // this.name would be undefined!

    // RIGHT WAY - using apply to preserve 'this' context
    const result = originalMethod.apply(this, args);

    console.log('After method call');
    return result;
  };
}

// Apply the decorator manually for demonstration
const descriptor = Object.getOwnPropertyDescriptor(SimpleCalculator.prototype, 'add');
if (descriptor) {
  BadDecorator(SimpleCalculator.prototype, 'add', descriptor);
}

const simpleCalc = new SimpleCalculator('MyCalculator');
simpleCalc.add(10, 20); // Notice how 'this.name' works correctly

// Example 3: Breaking down what apply does
console.log('\n=== BREAKING DOWN .apply() ===');

function demonstrateApply() {
  const originalFunction = function (x: number, y: number) {
    return `Object ${this.id}: ${x} + ${y} = ${x + y}`;
  };

  const contextObject = { id: 'ABC123' };
  const arguments_array = [5, 7];

  // These three calls are equivalent:

  // 1. Using apply
  const result1 = originalFunction.apply(contextObject, arguments_array);

  // 2. Using call (similar to apply but takes individual arguments)
  const result2 = originalFunction.call(contextObject, 5, 7);

  // 3. Manual way (what apply does internally)
  const result3 = (function () {
    // Temporarily set 'this' to contextObject and call the function
    return originalFunction.call(contextObject, arguments_array[0], arguments_array[1]);
  })();

  console.log('Result 1 (apply):', result1);
  console.log('Result 2 (call):', result2);
  console.log('Result 3 (manual):', result3);
  console.log('All results are the same:', result1 === result2 && result2 === result3);
}

demonstrateApply();

// ========================================
// PART 3: PROPERTY DECORATOR
// ========================================

// Step 5: Property decorator - runs when a property is defined
function LogProperty(target: any, propertyName: string) {
  // target: The class that contains the property
  // propertyName: The name of the property being decorated

  console.log(`Property ${propertyName} is being decorated`);

  // We can store the original value
  let value: any;

  // Define getter and setter for the property
  Object.defineProperty(target, propertyName, {
    get: function () {
      console.log(`Getting value of ${propertyName}: ${value}`);
      return value;
    },
    set: function (newValue: any) {
      console.log(`Setting ${propertyName} to: ${newValue}`);
      value = newValue;
    }
  });
}

class Person {
  @LogProperty
  name: string = '';

  @LogProperty
  age: number = 0;
}

// Test the decorated properties
const person = new Person();
person.name = 'Alice'; // Will log the setting
console.log(person.name); // Will log the getting

// ========================================
// PART 4: CLASS DECORATOR
// ========================================

// Step 6: Class decorator - modifies the entire class
function LogClass(constructor: Function) {
  // constructor: The class constructor function

  console.log(`Decorating class: ${constructor.name}`);

  // We can modify the class or return a new one
  return class extends (constructor as any) {
    constructor(...args: any[]) {
      console.log(`Creating instance of ${constructor.name}`);
      super(...args);
      console.log(`Instance of ${constructor.name} created`);
    }
  };
}

@LogClass
class Car {
  constructor(public brand: string) {
    console.log(`Car constructor called with brand: ${brand}`);
  }

  start() {
    console.log(`${this.brand} car is starting`);
  }
}

// Test the decorated class
const car = new Car('Toyota');
car.start();

// ========================================
// PART 5: DECORATOR WITH PARAMETERS
// ========================================

// Step 7: Decorator factory - a function that returns a decorator
function Retry(times: number) {
  // This function takes parameters and returns the actual decorator
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      let attempts = 0;

      while (attempts < times) {
        try {
          console.log(`Attempt ${attempts + 1} for ${propertyName}`);
          return originalMethod.apply(this, args);
        } catch (error) {
          attempts++;
          if (attempts >= times) {
            throw error;
          }
          console.log(`Attempt ${attempts} failed, retrying...`);
        }
      }
    };
  };
}

class NetworkService {
  @Retry(3) // This method will retry up to 3 times
  fetchData(): string {
    // Simulate random failure
    if (Math.random() > 0.7) {
      return 'Data fetched successfully!';
    } else {
      throw new Error('Network error');
    }
  }
}

// Test the retry decorator
const service = new NetworkService();
try {
  console.log(service.fetchData());
} catch (error) {
  console.log('All attempts failed');
}
