import { Utility } from '@shared/utils/utils';
import { AfterViewInit, ChangeDetectorRef, Component, HostListener, Input, OnChanges, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { EmployeeLookupApiResponse, FilterReport, Group, OpenPositionReport } from '@entities/utilization-management/utilization.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { ConfirmationService, LazyLoadEvent, TableState } from 'primeng/api';
import { Observable, Subject, forkJoin } from 'rxjs';
import moment from 'moment';
import { ManagePeopleService } from '../manage-people.service';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';
import { Employee, ISavedFilterList, QueryFilter, SaveFilter } from '@entities/project/project.model';
import { UtilizationService } from '@entities/utilization-management/utilization.service';
import { FormControl } from '@angular/forms';
import { AppConstants } from '@shared/constants';
import { ActivatedRoute, Params, Router } from '@angular/router';
import _ from 'lodash';
import { ProjectService } from '@entities/project/project.service';
import { DatePipe } from '@angular/common';

import { MangeTimeService } from './mange-time.service';
import { ColumnToggleService } from '@shared/services/column-toggle.service';
import { ConvertDate, formatDateToYYYYMMDD } from '@shared/utils/date-utils';
import { EditComment, Position, TimeEntry, TimeSheetDayObj } from './mange-time.model';
import { Calendar } from 'primeng/calendar';
import { OverlayPanel } from 'primeng/overlaypanel';
import { ComponentsType, FiledType } from '@shared/models/component-type-enum';
import { AdministrationService } from '@entities/administration/administration.service';
import { TIMESHEET_RANGE, VALUE_OF_WEEK_DAYS } from '@shared/constants/manage-timesheet.constant';
import { TimesheetStatus } from '@shared/enum/mange-timesheet.enum';
import { BookTimeOffComponent } from '@shared/components/book-time-off/book-time-off.component';
import { map } from 'rxjs/operators';
import filter from '../../../../assets/plugins/formvalidation/src/js/core/filter';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { AuthService } from '@auth/index';
import { promise } from 'protractor';
import { Table } from 'primeng/table';
import { Debounce } from '@shared/decorators/debounce.decorator';
import { Dropdown } from 'primeng/dropdown';
enum Visibility {
  Public = 'Public',
  System = 'System'
}

enum PageNameData {
  CreateTimeSheet = 'createTimesheet',
  MangeTimesheet = 'manageTimeSheet'
}

enum PageName {
  CreateTimeSheet = 'create-timesheet',
  MangeTimesheet = 'manage-time'
}

enum CurrentTab {
  Client = 'Client',
  Project = 'Project',
  Position = 'Position',
  Employee = 'Employee'
}

@Component({
  selector: 'app-manage-time',
  templateUrl: './manage-time.component.html',
  styleUrls: ['./manage-time.component.scss'],
  providers: [ConfirmationService, DatePipe, MangeTimeService]
})
export class ManageTimeComponent extends SflBaseComponent implements OnInit, OnDestroy {
  isPermissionManageTime = false;
  isFilterReset = false;
  showFilter = false;
  isApproved = false;
  selectedTimeSheetHourStatusHardCopy: TimesheetStatus;
  notIncludeCreateTimesheet = ['Employee', 'Status'];
  currentTabRecord: any;
  timesheetStatus = Object.keys(TimesheetStatus)?.map((key) => ({
    label: TimesheetStatus[key as keyof typeof TimesheetStatus],
    value: TimesheetStatus[key as keyof typeof TimesheetStatus]
  }));
  selectedFilterRangeQuery: any;
  selectedFilterRangeQueryCopy: any;
  selectedTimeSheetHourStatus: TimesheetStatus;
  timeSheetStatusEnum = TimesheetStatus;
  employeeData: any;
  bookTimeOffPopUp = false;
  workExceptionsData = [];
  loadingTimeOFF = false;
  recordData: TimeEntry[];
  exceptionTypes = [];
  filedType = FiledType;
  selectedColumnExport = [];
  showExportOptionDialog = false;
  exportPdfColumns = [];
  csvCols = [];
  excelExportReportData = [];
  excelHeaders = [];
  showExportOptions: boolean;
  positionNOTInRange = false;
  unableToDownloadPdf = false;
  openFilter = false;
  hasEmployeeEmail: boolean;
  resizeFlag = true;
  loadingTable = false;
  frozenCols = [];
  isShowHideColumns = false;
  userNotFound = false;
  pageName = PageName;
  activatedMode: PageName.CreateTimeSheet | PageName.MangeTimesheet;
  errorInForm = false;
  prepareFilterState: any;
  currentTab: CurrentTab | string = CurrentTab.Employee;
  currentTabName = CurrentTab;
  monthArrayExport = [];
  tags = [];
  showApprovedDialog = false;
  _pCols: string[] = [];
  cardTitle = 'Manage Time';
  selectedPositionOption;
  positionList = [];
  exportReportData = [];
  exportCopyMonth = [];
  dateError = false;
  extendFields: any;
  exportComments = [];
  button = {
    btnSvg: 'download-wt',
    btnClass: 'btn-filter-icon download',
    action: this.openExportOptionList.bind(this)
  };
  buttons: ButtonParams[] = [];
  splitButtonDropDownOption = {
    action: this.openSideBar.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.openSaveFilterList();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSaveFilter();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilter();
        }
      }
    ]
  };

  buttonsForSideBarFilter: ButtonParams[] = [
    {
      btnSvg: 'filter-list',
      btnClass: 'btn-filter-icon',
      action: this.openSaveFilterList.bind(this)
    },
    {
      btnSvg: 'save',
      btnClass: 'btn btn-sm btn-icon btn-icon-light svg-icon svg-icon-md icon-background mr-2 filter-btn-wrapper',
      action: this.onSaveFilter.bind(this)
    },
    {
      btnClass: 'btn-close-icon',
      btnIcon: 'times',
      action: this.closeSideBar.bind(this, true)
    }
  ];

  selectedColumns: Array<any>;

  // @Input() get selectedColumns(): any[] {
  //   let data;
  //   return this.activatedMode === this.pageName.CreateTimeSheet ? data = this._selectedColumns.filter(col => col.field === "Employee") : this._selectedColumns;
  // }

  // set selectedColumns(val: any) {
  //   setTimeout(() => {
  //     this.columnToggle.setSelectedColumns(
  //       this._selectedColumns,
  //       "createTimesheet"
  //     );
  //     const col = this._selectedColumns;
  //     if (col) {
  //       this._selectedColumns = col.filter((val) => val?.includes(val));
  //     } else {
  //       this._selectedColumns = this.frozenCols.filter((col) =>
  //         val.includes(col)
  //       );
  //     }
  //     this._pCols = col?.map((f) => f.field);
  //   }, 500);
  // }

  sidebarParams: SidebarParams<FilterReport>;
  @ViewChild('sidebarFilter', { static: true }) sidebarFilter: MatSidenav;
  @ViewChild('bookTimeOFF') bookTimeOff: BookTimeOffComponent;
  monthArray: any[];
  currentDate: Date = new Date();
  selectedMonth: any = new Date();
  userAddORUpdatedTime = [];

  //sidebar
  isFilterBySavedEmployeeFiltres = false;
  selectedEmployeeNameList = [];
  selectedEmployeeIds;
  employeeList: Employee[] = [];
  savedEmployeeFilterList: Group[];

  isFilterBySavedClientFiltres = false;
  clientList = [];
  selectedClientNameList = [];
  selectedClientIds;
  savedClientFilterList: Group[];
  activeIndexTab = 0;
  isFilterBySavedProjectFiltres = false;
  projectList = [];
  selectedProjectNameList = [];
  selectedProjectesIds;
  savedProjectFilterList: Group[];
  _selectedColumns: any;
  dataFilter: any = {};

  showFilterListDialog = false;
  sharedFilters: any[];
  filteredFilters: ISavedFilterList;
  myFilters: any[] = [];
  selectedFilterFormControl = new FormControl('');
  editFilterObj: QueryFilter;
  deleteFilterObj: QueryFilter;
  availableFilters = null;
  selectedFilter = null;
  shareFilterObj = null;
  showNameError = false;
  queryFilterId: number;
  showSavedFilter = false;
  showEditDialog = false;
  showShareDialog = false;
  showDeleteDialog = false;
  dateRequired = false;
  timeSheetRange = TIMESHEET_RANGE;
  timeSheetRangeone = Object?.values(this.timeSheetRange);
  selectedTimesheetRange = TIMESHEET_RANGE.WEEKLY;
  startDate: Date;
  endDate: Date;
  weekArray = [];
  biWeekArray = [];
  afterCalenderColumnGlobal = [];
  weekDays = 7;
  biWeekRange = 15;
  biweekDays = 14;
  sixDays = 6;
  selectedDate = new Date();
  selectedDateRange = [new Date()];
  two = 2;
  one = 1;
  addCommentText: string;
  editComment: EditComment = {} as EditComment;
  prevSelectedTimeRange = this.selectedTimesheetRange ?? TIMESHEET_RANGE.WEEKLY;
  totalHours = [];
  startDayValue = 1;
  totalWeeks = 1;
  dateColumns = [];
  isDisableComment = false;
  isSubmitted = false;
  isDraft = false;
  currentPage = this.appConstants.DEFAULT_PAGE;
  rowsPerPage = this.appConstants.DEFAULT_ROWS_PER_PAGE;
  sortFieldName: string = 'customer_name';
  sortOrderNumber: number = 1;
  sortColumnFlag: boolean = false;
  isHoverComment = false;
  isClickedToEditComment = false;
  @ViewChild('dt') table: Table;

  @ViewChild('calendar') calendar: Calendar;
  @ViewChild('op') op: OverlayPanel;

  constructor(
    private readonly authService: AuthService,
    private readonly confirmationService: ConfirmationService,
    private readonly managePeopleService: ManagePeopleService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly utilizationService: UtilizationService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef,
    private readonly projectService: ProjectService,
    private readonly datePipe: DatePipe,
    private readonly mangeTimeService: MangeTimeService,
    private readonly columnToggle: ColumnToggleService,
    private readonly adminService: AdministrationService,
    private readonly cacheFilter: GetSetCacheFiltersService
  ) {
    super();
  }

  customers: any;
  positionOpenOrAllList = [
    {
      key: 'Show Open Positions',
      value: true
    },
    {
      key: 'Show Staffed Positions',
      value: false
    },
    {
      key: 'Show All Positions',
      value: undefined
    }
  ];

  openSaveFilterList() {
    this.showFilterListDialog = true;
    this.showSavedFilter = true;
    this.cdr.detectChanges();
  }

  onSaveFilter() {
    // let filter = JSON.parse(JSON.stringify(this.dataFilter));
    // filter = this.queryStringUtil(filter);

    // if (this.dataFilter?.project_name?.name) {
    //   filter.project_grp_name = this.dataFilter?.project_name?.name;
    //   filter.project_grp_value = this.dataFilter?.project_name?.value;
    // }
    // if (this.dataFilter?.customer_name?.name) {
    //   filter.client_grp_name = this.dataFilter?.customer_name?.name;
    //   filter.client_grp_value = this.dataFilter?.customer_name?.value;
    // }
    // if (this.dataFilter?.selectedSavedEmployeeFilter?.name) {
    //   filter.emp_grp_name = this.dataFilter?.selectedSavedEmployeeFilter?.name;
    //   filter.emp_grp_value =
    //     this.dataFilter?.selectedSavedEmployeeFilter?.value;
    // }

    // let filter= {
    //   startDate: this.startDate,
    //   endDate :this.endDate,
    //   timesheetStatus: TimesheetStatus.SUBMITTED
    // }
    this.selectedFilterRangeQuery = {};
    this.selectedFilterRangeQuery = {
      startDate: this.startDate,
      endDate: this.endDate,
      timesheet: this.selectedTimesheetRange,
      selectedTab: this.currentTab,
      timesheetStatus: this.selectedTimeSheetHourStatus || TimesheetStatus.ALL
    };

    this.selectedFilterRangeQuery?.selectedTab
      ? (this.selectedFilterRangeQuery = {
          ...this.selectedFilterRangeQuery,
          ...this.filterSaveProcess(this.currentTab, true),
          selectedTabValue: this.filterSaveProcess(this.currentTab, true),
          isAnyTabSelected: this.filterSaveProcess(this.currentTab, true) ? true : false
        })
      : {};
    let filter = this.selectedFilterRangeQuery;

    const requestObject: SaveFilter = {
      query_string: this.serialize(filter),
      resource: 'manageTime'
    };
    const dialogTitle = 'Save Filter Group';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject);
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        setTimeout(() => {
          this.sidebarParams?.template?.close();
          this.openFilter = false;
        }, 500);
        this.getStoredFilters();
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }

  getStoredFilters() {
    const requestObject = {
      resource: 'manageTime'
    };
    this.subscriptionManager.add(
      this.utilizationService.getStoredFilters(requestObject).subscribe((res: ISavedFilterList) => {
        this.sharedFilters = [];
        this.myFilters = [];
        this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
        this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
        this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
        this.availableFilters = res.data.query_filters;
        this.cdr.detectChanges();
        this.routerListener();
      })
    );
  }

  serialize = (obj) => {
    const str = [];
    for (const p in obj) {
      if (obj.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
      }
    }
    return str.join('&');
  };

  queryStringUtil(queryStringParam: any) {
    const queryFilter: any = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined || queryStringParam[key].lenght === 0) {
          delete queryStringParam[key];
        }
      }
    }
    return queryStringParam;
  }

  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter.length
      ? this.availableFilters.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === false);
  }

  inputFilterName() {
    this.showNameError = false;
  }

  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }

  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Share',
      text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = {
      ...filterOption,
      header: 'Unshare',
      text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?`
    };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        () => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.cdr.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Manage Time');
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  deleteFilter(filterOption) {
    this.showDeleteDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        () => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.cdr.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Manage Time');
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.utilizationService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          () => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.showFilterListDialog = true;
            this.cdr.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      }
    });
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            const filterValue = this.sharedFilters?.find((f) => JSON.stringify(f) === JSON.stringify(this.selectedFilter));
            if (filterValue) {
              this.selectedFilterFormControl.setValue(filterValue);
            }
            this.applyFilter();
          }
        },
        () => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  applySelectedFilterAndUpdateUrl() {
    this.showFilterListDialog = false;
    this.showSavedFilter = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.closeSideBar();
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filterId: this.selectedFilter?.query_filter?.id },
      queryParamsHandling: 'merge'
    });
  }

  closeModal() {
    this.showFilterListDialog = false;
    this.selectedFilter = null;
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
  }

  applyFilter() {
    this.showSavedFilter = false;
    this.dataFilter = JSON.parse('{"' + decodeURI(this.selectedFilter.query_filter.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    this.applyAllFilter();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters;
    this.closeModal();
  }

  applyAllFilter() {
    // if (this.dataFilter.hasOwnProperty("emp_grp_name")) {
    //   this.dataFilter.selectedSavedEmployeeFilter = {
    //     name: this.dataFilter.emp_grp_name,
    //     value: this.dataFilter.emp_grp_value
    //       .replace(/%3D/g, "=")
    //       .replace(/%26/g, "&"),
    //   };
    //   this.isFilterBySavedEmployeeFiltres = true;
    //   this.dataFilter.employee_ids = this.dataFilter.employee_ids.replace(
    //     /%2C/g,
    //     ","
    //   );
    // if(this.dataFilter.hasOwnProperty("startDate") && this.dataFilter.hasOwnProperty("endDate") ){
    // }

    if (this.dataFilter.hasOwnProperty('timesheetStatus')) {
      this.selectedTimeSheetHourStatus = this.dataFilter?.timesheetStatus;
    }
    if (this.dataFilter.hasOwnProperty('isAnyTabSelected') && this.dataFilter.isAnyTabSelected) {
      this.currentTab = this.dataFilter.selectedTab;
      this.selectedTimesheetRange = this.dataFilter.timesheet;
      let value;
      if (this.currentTab === this.currentTabName.Project) {
        this.dataFilter.label = this.dataFilter?.label?.replace(/%3A/g, ':');
        value = this.projectList?.find((elist) => elist.value === this.dataFilter.value);
      }
      if (this.currentTab === this.currentTabName.Position) {
        value = this.positionList?.find((pos) => pos.value == this.dataFilter.value);
      }
      value = value ? value : { label: this.dataFilter.label, value: this.dataFilter.value };
      this.filterSaveProcess(this.currentTab, false, value);
    }

    this.selectedFilterRangeQuery = {
      startDate: this.startDate,
      endDate: this.endDate,
      timesheet: this.selectedTimesheetRange,
      selectedTab: this.currentTab
    };
    this.filterApply();
  }

  // if (this.dataFilter.hasOwnProperty("employeeName")) {
  //   this.dataFilter.employee_ids = this.dataFilter.employee_ids.replace(
  //     /%2C/g,
  //     ","
  //   );
  //   this.isFilterBySavedEmployeeFiltres = false;
  //   this.selectedEmployeeIds = this.dataFilter.employee_ids.split(",");
  //   this.selectedEmployeeNameList = this.dataFilter.employeeName
  //     .toString()
  //     .replace(/%2C/g, ",")
  //     .split(",");
  // }

  // if (this.dataFilter.hasOwnProperty("client_grp_name")) {
  //   this.dataFilter.selectedSavedClientFilter = {
  //     name: this.dataFilter.client_grp_name,
  //     value: this.dataFilter.client_grp_value
  //       .replace(/%3D/g, "=")
  //       .replace(/%26/g, "&"),
  //   };
  //   this.isFilterBySavedClientFiltres = true;
  //   this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(
  //     /%2C/g,
  //     ","
  //   );
  // }
  // if (this.dataFilter.hasOwnProperty("customer_ids")) {
  //   this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(
  //     /%2C/g,
  //     ","
  //   );
  //   this.selectedClientIds = this.dataFilter.customer_ids.split(",");
  //   this.selectedClientNameList = this.dataFilter.ClientName.toString()
  //     .replace(/%2C/g, ",")
  //     .split(",");
  //   this.isFilterBySavedClientFiltres = false;
  // }

  // if (this.dataFilter.hasOwnProperty("project_grp_name")) {
  //   this.dataFilter.selectedSavedProjectFilter = {
  //     name: this.dataFilter.project_grp_name,
  //     value: this.dataFilter.project_grp_value
  //       .replace(/%3D/g, "=")
  //       .replace(/%26/g, "&"),
  //   };
  //   this.isFilterBySavedProjectFiltres = true;
  //   this.dataFilter.project_ids = this.dataFilter.project_ids.replace(
  //     /%2C/g,
  //     ","
  //   );
  // }
  // if (this.dataFilter.hasOwnProperty("project_ids")) {
  //   this.dataFilter.project_ids = this.dataFilter.project_ids.replace(
  //     /%2C/g,
  //     ","
  //   );
  //   this.selectedProjectesIds = this.dataFilter.project_ids.split(",");
  //   this.selectedProjectNameList = this.dataFilter.projectName
  //     .toString()
  //     .replace(/%2C/g, ",")
  //     .split(",");
  //   this.isFilterBySavedProjectFiltres = false;
  // }

  ngOnInit() {
    this.getGlobalDetail();
    this.changePageMode();
    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      this.activatedRoute.queryParams.subscribe((params) => {
        params['date'] ? (this.selectedDate = this.currentDate = new Date(params['date'])) : '';
      });
      this.createTimesheetFlow();
      this.mangeTimeFlow();
      this.getExceptionTypes();
    } else if (this.activatedMode === this.pageName.MangeTimesheet) {
      this.checkEmployeeFilterStatus(this.appConstants.MANAGE_SCREENS.MANAGE_TIME);
      if (this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.MANAGE_TIME)) {
        this.dataFilter = this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.MANAGE_TIME);
        this.rowsPerPage = this.dataFilter.rowsPerPage || this.appConstants.DEFAULT_ROWS_PER_PAGE;
        this.currentPage = this.dataFilter.currentPage || this.appConstants.DEFAULT_PAGE;
        const [sortOrder, sortFieldName] = this.dataFilter?.order_by?.split(':');
        this.sortOrderNumber = sortOrder === 'asc' ? 1 : -1;
        this.sortFieldName = sortFieldName || 'customer_name';
      } else {
        this.currentPage = this.appConstants.DEFAULT_PAGE;
        this.rowsPerPage = this.appConstants.DEFAULT_ROWS_PER_PAGE;
        this.sortFieldName = 'customer_name';
        this.sortOrderNumber = 1;
      }

      this.selectedTimeSheetHourStatus = TimesheetStatus.ALL;
      this.getApis();
      this.setFilter();
      this.mangeTimeFlow(false);
      this.buttons.push(this.button);
    }
    this.getGlobalDetailsCategory();
    this.checkManageTimePermission();
  }

  getApis() {
    this.getPositionList();
    this.getEmployeeList();
    this.getClientList();
    this.getProjectList();
    this.getStoredFilters();
    this.cdr.detectChanges();
  }

  getEmployeeList() {
    const queryFilter = {
      order_by: 'asc:first_name',
      employee_status: 'active'
    };
    this.managePeopleService.getEmployeeList(queryFilter).subscribe((res) => {
      res?.data?.employees?.forEach((e) => {
        this.employeeList.push({
          label: e.employee?.name,
          value: e.employee.id.toString()
        });
      });
      this.sortList(this.employeeList);
    });
  }

  getSavedEmployeeFilters() {
    const requestObject = {
      resource: 'employees'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const employeeGrp = [];
        response?.data?.query_filters?.map((query) => {
          employeeGrp.push({
            label: query.query_filter.name,
            value: {
              name: query.query_filter.name,
              value: query.query_filter.query_string
            }
          });
        });
        this.savedEmployeeFilterList = employeeGrp;
        this.sortList(this.savedEmployeeFilterList);
        this.cdr.detectChanges();
      })
    );
  }

  getEmployeeIds(event: any) {
    const paramsToRemove = ['offset', 'limit'];
    const param = this.removeParams(event.value.value.split('&'), paramsToRemove);
    this.subscriptionManager.add(
      this.utilizationService.getEmployeeIds(param).subscribe((res) => {
        if (res?.employee_ids) {
          this.dataFilter.employee_ids = res.employee_ids.join(',');
        } else {
          this.dataFilter.employee_ids = '';
        }
        this.cdr.detectChanges();
      })
    );
    this.dataFilter.emp_grp_name = this.dataFilter.selectedSavedEmployeeFilter.name;
    this.dataFilter.emp_grp_value = this.dataFilter.selectedSavedEmployeeFilter.value;
  }

  employeeSelected(event) {
    this.dataFilter.employee_ids = event?.value?.toString();
    let employeeName = this.employeeList.filter((elist) => elist.value == event.itemValue);
    if (employeeName.length && employeeName[0]?.label) {
      if (this.selectedEmployeeNameList.includes(employeeName[0]?.label)) {
        this.selectedEmployeeNameList = this.selectedEmployeeNameList.filter((emp) => emp !== employeeName[0].label);
      } else {
        this.selectedEmployeeNameList.push(employeeName[0]?.label);
      }
    }
    this.dataFilter.employeeName = this.selectedEmployeeNameList;
  }

  clientSelected(event) {
    this.dataFilter.customer_ids = event?.value?.value?.toString();
    let clientName = this.clientList.filter((elist) => elist.value == event?.value?.value?.toString());
    if (clientName.length && clientName[0]?.label) {
      if (this.selectedClientNameList.includes(clientName[0]?.label)) {
        this.selectedClientNameList = this.selectedClientNameList.filter((emp) => emp !== clientName[0].label);
      } else {
        this.selectedClientNameList.push(clientName[0]?.label);
      }
    }
    this.dataFilter.ClientName = this.selectedClientNameList;
  }

  getProjectIds(event) {
    const paramsToRemove = ['offset', 'limit'];
    const param = this.removeParams(event.value.value.split('&'), paramsToRemove);
    this.subscriptionManager.add(
      this.utilizationService.getProjectIds(param).subscribe((res) => {
        if (res?.project_ids) {
          this.dataFilter.project_ids = res.project_ids.join(',');
        } else {
          this.dataFilter.project_ids = '';
        }
        this.cdr.detectChanges();
      })
    );
    this.dataFilter.project_grp_name = this.dataFilter.selectedSavedProjectFilter.name;
    this.dataFilter.project_grp_value = this.dataFilter.selectedSavedProjectFilter.value;
  }

  projectSelected(event) {
    this.dataFilter.project_ids = event?.value?.toString();
    let projectName = this.projectList.filter((elist) => elist.value == event.itemValue);
    if (projectName.length && projectName[0]?.name) {
      if (this.selectedProjectNameList.includes(projectName[0]?.name)) {
        this.selectedProjectNameList = this.selectedProjectNameList.filter((emp) => emp !== projectName[0].name);
      } else {
        this.selectedProjectNameList.push(projectName[0]?.name);
      }
    }
    this.dataFilter.projectName = this.selectedProjectNameList;
  }

  getClientIds(event) {
    const paramsToRemove = ['offset', 'limit'];
    const param = this.removeParams(event.value.value.split('&'), paramsToRemove);
    this.subscriptionManager.add(
      this.utilizationService.getClientIds(param).subscribe((res) => {
        if (res?.customer_ids) {
          this.dataFilter.customer_ids = res.customer_ids.join(',');
        } else {
          this.dataFilter.customer_ids = '';
        }
        this.cdr.detectChanges();
      })
    );
    this.dataFilter.client_grp_name = this.dataFilter.selectedSavedClientFilter.name;
    this.dataFilter.client_grp_value = this.dataFilter.selectedSavedClientFilter.value;
  }

  getClientList() {
    this.managePeopleService.getClientData({ is_active: true, order_by: 'asc:name' }).subscribe((res) => {
      const client = [];
      res?.body?.data?.customers?.map((c) => {
        client.push({
          label: c.customer.name,
          value: String(c.customer.id)
        });
      });
      this.clientList = client;
      this.sortList(this.clientList);
      this.cdr.detectChanges();
    });
  }

  getSavedClientFilters() {
    const requestObject = {
      resource: 'customers'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const clientGrp = [];
        response?.data?.query_filters?.map((query) => {
          clientGrp.push({
            label: query.query_filter.name,
            value: {
              name: query.query_filter.name,
              value: query.query_filter.query_string
            }
          });
        });
        this.savedClientFilterList = clientGrp;
        this.sortList(this.savedClientFilterList);
        this.cdr.detectChanges();
      })
    );
  }

  getProjectList() {
    this.managePeopleService.getProjectList().subscribe((res) => {
      res.data.projects.forEach((project) => {
        this.projectList.push({
          label: `${project.project?.customer.name} : ${project.project.name}`,
          name: project.project.name,
          value: project.project.id.toString(),
          status: project?.project?.status
        });
      });
      this.sortList(this.projectList);
      this.cdr.detectChanges();
    });
  }

  getSavedProjectFilters() {
    const requestObject = {
      resource: 'projects'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const projectGrp = [];
        response?.data?.query_filters?.map((query) => {
          projectGrp.push({
            label: query.query_filter.name,
            value: {
              name: query.query_filter.name,
              value: query.query_filter.query_string
            }
          });
        });
        this.savedProjectFilterList = projectGrp;
        this.sortList(this.savedProjectFilterList);
        this.cdr.detectChanges();
      })
    );
  }

  sortList(sortList) {
    if (this.sortList.length > 0) {
      sortList.sort((a, b) => {
        const fa = a?.label?.toLowerCase();
        const fb = b?.label?.toLowerCase();
        if (fa < fb) {
          return -1;
        }
        if (fa > fb) {
          return 1;
        }
        return 0;
      });
    }
  }

  removeParams(params, paramsToRemove) {
    return params
      .filter((param) => {
        const [key, value] = param.split('=');
        return !paramsToRemove.includes(key) && value !== '' && value !== null;
      })
      .join('&');
  }

  showSavedEmployeeFilterList(isReset?: boolean) {
    this.isFilterBySavedEmployeeFiltres = !this.isFilterBySavedEmployeeFiltres;
    this.dataFilter.employee_ids = null;
    this.dataFilter.employeeName = [];
    this.dataFilter.selectedSavedEmployeeFilter = null;
    this.dataFilter.emp_grp_name = null;
    this.dataFilter.emp_grp_value = null;
    this.selectedEmployeeIds = null;
    this.cdr.detectChanges();
    if (isReset) {
      this.isFilterBySavedEmployeeFiltres = false;
    }
  }

  showSavedClientFilterList(isReset?: boolean) {
    this.isFilterBySavedClientFiltres = !this.isFilterBySavedClientFiltres;
    this.dataFilter.customer_ids = null;
    this.dataFilter.selectedSavedClientFilter = null;
    this.selectedClientNameList = [];
    this.dataFilter.ClientName = [];
    this.dataFilter.client_grp_name = null;
    this.dataFilter.client_grp_value = null;
    this.selectedClientIds = null;
    this.cdr.detectChanges();
    if (isReset) {
      this.isFilterBySavedClientFiltres = false;
    }
  }
  showSavedProjectFilterList(isReset?: boolean) {
    this.isFilterBySavedProjectFiltres = !this.isFilterBySavedProjectFiltres;
    this.dataFilter.project_ids = null;
    this.dataFilter.projectName = [];
    this.selectedEmployeeNameList = [];
    this.selectedProjectesIds = null;
    this.dataFilter.project_grp_name = null;
    this.dataFilter.project_grp_value = null;
    this.dataFilter.selectedSavedProjectFilter = null;
    this.cdr.detectChanges();
    if (isReset) {
      this.isFilterBySavedProjectFiltres = false;
    }
  }

  resetFilter(): void {
    this.isFilterReset = true;
    this.selectedFilter = null;
    this.customers = [];
    this.tags = [];
    this.selectedClientIds = [];
    this.selectedProjectesIds = [];
    this.selectedEmployeeIds = [];
    this.selectedPositionOption = [];
    this.selectedTimeSheetHourStatus = TimesheetStatus.ALL;
    this.resetRemember();
    setTimeout(() => {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { filterId: null },
        queryParamsHandling: 'merge'
      });
      this.defaultAllDataFilter();
    }, 100);
  }

  openSideBar(): void {
    this.openFilter = true;
    this.rememberChanges();
  }

  closeSideBar(remember: boolean = false): void {
    this.openFilter = false;
    this.selectedTimesheetRange = this.prevSelectedTimeRange;
    this.generateMonthArray();
    if (remember) {
      this.rememberApplyChanges();
    }
  }

  generateMonthArray(): boolean {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    this.generateTimeRangeArray(this.selectedDate);

    this.monthArray = Array.from({ length: daysInMonth }, (_, index) => {
      const day = index + 1;
      const formattedDate = `${day} ${this.getMonthAbbreviation(month)}`;
      const date = new Date(year, month, day);
      return { label: formattedDate, fullDate: date };
    });

    if (TIMESHEET_RANGE.MONTHLY === this.selectedTimesheetRange) {
      this.prevSelectedTimeRange = this.selectedTimesheetRange;
      this.startDate = new Date(this.monthArray[0]?.fullDate);
      this.endDate = new Date(this.monthArray[this.monthArray?.length - 1]?.fullDate);
      this.selectedMonth = new Date(this.currentDate);
    }
    return true;
  }

  generateWeekArray(): void {
    this.startDate = new Date(this.getStartDate());
    const days = this.selectedTimesheetRange === TIMESHEET_RANGE.WEEKLY ? this.weekDays : this.biWeekRange;

    const daysArray = Array.from({ length: days }, (_, index) => {
      const currentDate = new Date(this.startDate);
      currentDate?.setDate(this.startDate?.getDate() + index);

      const formattedDate = `${currentDate?.getDate()} ${this.getMonthAbbreviation(currentDate?.getMonth())}`;
      return { label: formattedDate, fullDate: currentDate };
    });

    if (this.selectedTimesheetRange === TIMESHEET_RANGE.WEEKLY) {
      this.weekArray = daysArray;
    } else if (this.selectedTimesheetRange === TIMESHEET_RANGE.BIWEEKLY) {
      this.biWeekArray = daysArray;
    }
  }

  getMonthAbbreviation(month: number): string {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return monthNames[month];
  }

  previousMonth(): void {
    if (TIMESHEET_RANGE.MONTHLY === this.selectedTimesheetRange) {
      this.currentDate?.setMonth(this.currentDate?.getMonth() - 1);
    } else {
      this.selectedDate = new Date(this.startDate);
      if (TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange) {
        this.selectedDate?.setDate(this.startDate?.getDate() - this.weekDays * this.totalWeeks + 1);
      } else if (TIMESHEET_RANGE.BIWEEKLY === this.selectedTimesheetRange) {
        const endDate = new Date(this.startDate?.getFullYear(), this.startDate?.getMonth(), 0);
        const daysInBiWeek = this.startDate?.getDate() <= this.biWeekRange ? endDate?.getDate() - this.biWeekRange : this.biWeekRange;
        this.selectedDate?.setDate(this.startDate?.getDate() - daysInBiWeek + 1);
      }
      this.generateTimeRangeArray(this.selectedDate);
    }

    this.selectedMonth = new Date(this.currentDate);
    const data = this.generateMonthArray();
    if (data) {
      this.filterApply();
    }
  }

  isWeekend(date: Date): boolean {
    const dayOfWeek = date?.getDay();
    return dayOfWeek === 0 || dayOfWeek === 6;
  }

  nextMonth(): void {
    if (TIMESHEET_RANGE.MONTHLY === this.selectedTimesheetRange) {
      this.currentDate?.setMonth(this.currentDate?.getMonth() + 1);
    } else {
      this.selectedDate = new Date(this.startDate);
      if (TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange) {
        this.selectedDate?.setDate(this.startDate?.getDate() + this.weekDays * this.totalWeeks + 1);
      } else if (TIMESHEET_RANGE.BIWEEKLY === this.selectedTimesheetRange) {
        const endDate = new Date(this.startDate?.getFullYear(), this.startDate?.getMonth() + 1, 0);
        const daysInBiWeek = this.startDate?.getDate() > this.biWeekRange ? endDate.getDate() - this.biWeekRange : this.biWeekRange;
        this.selectedDate?.setDate(this.startDate?.getDate() + daysInBiWeek + 1);
      }
      this.generateTimeRangeArray(this.selectedDate);
    }
    const data = this.generateMonthArray();
    this.selectedMonth = new Date(this.currentDate);
    if (data) {
      this.filterApply();
    }
  }

  setMonth(event: any): void {
    this.currentDate?.setMonth(this.selectedMonth?.getMonth());
    if (this.activatedMode !== this.pageName.CreateTimeSheet) {
      this.selectedTimesheetRange = TIMESHEET_RANGE.MONTHLY;
    }
    const data = this.generateMonthArray();
    if (data) {
      this.filterApply();
    }
  }

  validateNumberInput(event: any, day: any, i: number, comment?: string, hours?: number): void {
    const pattern = this.appConstants.regexForTwoDecimalValidation;

    if (comment) {
      const addedTime = {
        date: moment(day).format(this.appConstants.dateFormate_YYYY_MM_DD),
        hours: hours ?? event?.target?.value,
        position_id: this.customers[i]?.position?.id,
        comment: comment ?? null,
        status: this.activatedMode === this.pageName.CreateTimeSheet ? TimesheetStatus.Draft : TimesheetStatus.APPROVED
      };
      const indexForAlredayAddPositionHours = this.userAddORUpdatedTime?.findIndex(
        (res) => res.position_id === this.customers[i]?.position?.id && res.date === moment(day).format(this.appConstants.dateFormate_YYYY_MM_DD)
      );

      if (indexForAlredayAddPositionHours !== -1) {
        this.userAddORUpdatedTime[indexForAlredayAddPositionHours].hours = hours ?? 0;
        this.userAddORUpdatedTime[indexForAlredayAddPositionHours].comment = comment;
      } else {
        this.userAddORUpdatedTime.push(addedTime);
      }
    } else {
      if (!pattern.test(event?.target?.value) || parseFloat(event?.target?.value) < 0 || parseFloat(event?.target?.value) > 24) {
        event?.preventDefault();
        event.target.value = this.customers[i]?.position[day?.label] ?? '';
      } else {
        const addedTime = {
          date: moment(day.fullDate).format(this.appConstants.dateFormate_YYYY_MM_DD),
          hours: hours ?? event?.target?.value,
          position_id: this.customers[i]?.position?.id,
          comment: this.getComment(day, this.customers[i]?.position?.time_entries, i) ?? this.editComment?.comment ?? null,
          status: this.activatedMode === this.pageName.CreateTimeSheet ? TimesheetStatus.Draft : TimesheetStatus.APPROVED
        };
        const indexForAlredayAddPositionHours = this.userAddORUpdatedTime.findIndex(
          (res) => res.position_id === this.customers[i]?.position?.id && res.date === moment(day?.fullDate).format(this.appConstants.dateFormate_YYYY_MM_DD)
        );
        if (indexForAlredayAddPositionHours !== -1) {
          this.userAddORUpdatedTime[indexForAlredayAddPositionHours].hours = event?.target?.value;
          this.userAddORUpdatedTime[indexForAlredayAddPositionHours].comment = this.userAddORUpdatedTime[indexForAlredayAddPositionHours]?.comment;
        } else {
          this.userAddORUpdatedTime.push(addedTime);
        }
      }
    }
    this.editComment = {} as EditComment;
  }

  validatePaste(event: any, day: any, index: number): void {
    const clipboardData = event.clipboardData || (window as any).clipboardData;
    const pastedData = clipboardData.getData('text');

    if (!this.isValidNumber(pastedData) || parseFloat(pastedData) < 0 || parseFloat(pastedData) > 24) {
      event.preventDefault();
    } else {
      const addedTime = {
        date: moment(day.fullDate).format(this.appConstants.dateFormate_YYYY_MM_DD),
        hours: event.target.value,
        position_id: this.customers[index].position.id
      };
      const indexForAlredayAddPositionHours = this.userAddORUpdatedTime.findIndex(
        (res) => res.position_id === this.customers[index].position.id && res.date === moment(day.fullDate).format(this.appConstants.dateFormate_YYYY_MM_DD)
      );
      if (indexForAlredayAddPositionHours !== -1) {
        this.userAddORUpdatedTime[indexForAlredayAddPositionHours].hours = event.target.value;
      } else {
        this.userAddORUpdatedTime.push(addedTime);
      }
      this.customers[index].position[day.label] = event.target.value;
    }
  }

  isValidNumber(value: string): boolean {
    const pattern = /^\d*(\.\d{0,2})?$/;
    return pattern.test(value);
  }

  onEnter(event: KeyboardEvent, i: number, day: TimeSheetDayObj): void {
    const inputElement = event?.target as HTMLInputElement;
    if (event.key === 'Enter' && inputElement?.value) {
      this.customers[i].position[day.label] = parseFloat(inputElement?.value)?.toFixed(2);
      this.updateObjBeforeSave(i);
    }
  }

  onBlur(event: FocusEvent, i: number, day: TimeSheetDayObj): void {
    const inputElement = event?.target as HTMLInputElement;
    if (!Number.isNaN(parseFloat(inputElement.value))) {
      this.customers[i].position[day.label] = parseFloat(inputElement?.value)?.toFixed(2);
      this.updateObjBeforeSave(i);
    } else {
      inputElement.value = this.customers[i]?.position[day?.label] ?? '';
      this.userAddORUpdatedTime = [];
    }
  }

  updateObjBeforeSave(i: number): void {
    if (this.userAddORUpdatedTime) {
      let flag = true;
      this.customers[i]?.position?.time_entries?.forEach((time_entry) => {
        const time_entry_date = this.datePipe.transform(time_entry?.time_entry?.date, AppConstants.format);
        const userAddORUpdatedTime_date = this.datePipe.transform(this.userAddORUpdatedTime[0]?.date, AppConstants.format);
        if (time_entry_date === userAddORUpdatedTime_date && this.userAddORUpdatedTime[0]) {
          time_entry.time_entry = this.userAddORUpdatedTime[0];
          flag = false;
        }
      });

      if (flag && this.userAddORUpdatedTime[0]) {
        this.customers[i]?.position?.time_entries.push({ time_entry: this.userAddORUpdatedTime[0] });
      }
      this.saveData();
    }
  }

  saveAddedLogs(): void {
    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      this.userAddORUpdatedTime = [];
      this.customers?.forEach((customer) => {
        customer?.position?.time_entries?.forEach((time_entry) => {
          if (time_entry?.time_entry?.status === TimesheetStatus.Draft) {
            time_entry.time_entry.status = TimesheetStatus.SUBMITTED;
            delete time_entry?.time_entry?.id;
            this.isSubmitted = true;
            this.isDraft = false;
            this.userAddORUpdatedTime.push(time_entry?.time_entry);
          }
        });
      });
      this.saveData();
    }
  }

  onUnSubmitAll(): void {
    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      this.userAddORUpdatedTime = [];
      this.customers?.forEach((customer) => {
        customer?.position?.time_entries?.forEach((time_entry) => {
          if (time_entry?.time_entry?.status === TimesheetStatus.SUBMITTED) {
            time_entry.time_entry.status = TimesheetStatus.Draft;
            delete time_entry?.time_entry?.id;
            this.isSubmitted = false;
            this.isDraft = true;
            this.userAddORUpdatedTime.push(time_entry?.time_entry);
          }
        });
      });
      this.saveData();
    }
  }

  saveData(refresh?: boolean): void {
    const apiCalls = this.userAddORUpdatedTime?.map((data) => {
      const param = {
        time_entry: {
          ...data
        }
      };
      return this.managePeopleService.saveTimeSheetEntries(param);
    });

    forkJoin(apiCalls).subscribe({
      next: (responses: any) => {
        const allSuccess = responses.every((res) => res['success']);
        if (allSuccess) {
          this.layoutUtilsService.showActionNotification(this.appConstants.timeEntryDataSave, AlertType.Success);
          this.totalHours = [];
          this.calculateTotalHours();
          this.closeApprovePopup();
          this.setExportHeaders(this.customers);
          this.updateTimesheetObj(this.customers);
          this.showApprovedDialog;
          if (refresh) {
            this.filterApply();
          }
          this.isSubmitted = false;
          this.isDraft = false;
          this.customers?.forEach((position) => {
            this.isSubmittedTimesheet(position?.position?.time_entries);
          });
        }
        this.userAddORUpdatedTime = [];
      },
      error: () => {
        this.userAddORUpdatedTime = [];
      }
    });
  }

  canDeactivate(): Observable<boolean> | boolean {
    if (this.userAddORUpdatedTime.length >= 1) {
      return this.showAlert({});
    } else {
      return true;
    }
  }

  showAlert({ actions }: { actions?: { acceptAction: () => void; rejectAction?: () => void } }): Observable<boolean> {
    const subject = new Subject<boolean>();

    this.confirmationService.confirm({
      message: `Your changes haven't been saved. Do you want to proceed further?`,
      header: 'Alert',
      icon: 'pi pi-info-circle',
      acceptIcon: 'none',
      rejectIcon: 'none',
      rejectButtonStyleClass: 'p-button-text',
      accept: () => {
        if (actions?.acceptAction) {
          actions?.acceptAction();
        }
        subject.next(true);
        subject.complete();
      },
      reject: () => {
        subject.next(false);
        subject.complete();
      },
      key: 'conformationDialog'
    });

    return subject.asObservable();
  }

  endMonthSelected(event) {
    this.dateError = false;
    this.dateRequired = false;
    let date = new Date(event);
    date = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    this.dataFilter.util_end_date = this.datePipe.transform(date, AppConstants.format);
    this.dataFilter.end_month = moment(this.dataFilter.util_end_date).toDate();
    if (this.dataFilter.util_start_date && this.dataFilter.util_end_date) {
      if (new Date(this.dataFilter.util_start_date) > new Date(this.dataFilter.util_end_date)) {
        this.dateError = true;
      }
    }
  }

  startMonthSelected(event) {
    this.dateError = false;
    this.dateRequired = false;
    this.dataFilter.util_start_date = this.datePipe.transform(event, AppConstants.format);
    this.dataFilter.start_month = moment(this.dataFilter.util_start_date).toDate();
    if (this.dataFilter.util_start_date && this.dataFilter.util_end_date) {
      if (new Date(this.dataFilter.util_start_date) > new Date(this.dataFilter.util_end_date)) {
        this.dateError = true;
      }
    }
  }
  // initially set filter when page is load
  setFilter(): void {
    this.dataFilter.rollingOption = 'Current plus 1 months';
  }

  getPositionList(): void {
    this.subscriptionManager.add(
      this.utilizationService.getPositionList().subscribe((res) => {
        res?.data?.positions?.map((position) => {
          this.positionList.push({
            label: position.position.name,
            value: position.position.id
          });
        });

        this.sortList(this.positionList);
        this.dataFilter.position_ids = this.dataFilter.position_ids;
        if (this.dataFilter.position_ids) {
          const position = this.positionList.find((position) => position.value === Number(this.dataFilter.position_ids));
          this.selectedPositionOption = position;
        }
      })
    );
  }

  onPositionChange(e: any): void {
    if (e.value) {
      this.selectedPositionOption = e.value;
      this.dataFilter.position_ids = e.value.value;
    } else {
      this.selectedPositionOption = null;
      this.dataFilter.position_ids = null;
    }
  }

  filterApply(dateReset: boolean = true): void {
    if (dateReset) {
      this.startDate = null;
      this.endDate = null;
    }
    this.updateDateRange();
    this.generateMonthArray();
    if (this.getStartDate() && this.getEndDate()) {
      switch (this.currentTab) {
        case this.currentTabName.Position: {
          if (this.selectedPositionOption?.value) {
            this.errorInForm = false;
            this.selectedClientIds = [];
            this.selectedProjectesIds = [];
            this.selectedEmployeeIds = [];

            this.getTimesheet({ position_ids: this.selectedPositionOption.value }, this.currentTabName.Position, this.selectedPositionOption.label);
            break;
          }
        }

        case this.currentTabName.Client: {
          if (this.selectedClientIds?.value) {
            this.errorInForm = false;
            this.selectedPositionOption = [];
            this.selectedProjectesIds = [];
            this.selectedEmployeeIds = [];
            const clientId = this.selectedClientIds?.value || this.dataFilter.value;
            const clientName = this.clientList?.find((elist) => elist.value === clientId)?.label || this.dataFilter.label;
            this.getTimesheet({ customer_name: clientName }, this.currentTabName.Client, this.selectedClientIds.label);
            break;
          }
        }

        case this.currentTabName.Project: {
          if (this.selectedProjectesIds?.value) {
            this.errorInForm = false;
            this.selectedPositionOption = [];
            this.selectedClientIds = [];
            this.selectedEmployeeIds = [];
            this.getTimesheet({ project_ids: this.selectedProjectesIds.value }, this.currentTabName.Project, this.selectedProjectesIds.label);
            break;
          }
        }

        case this.currentTabName.Employee: {
          if (this.selectedEmployeeIds?.value) {
            this.errorInForm = false;
            this.selectedPositionOption = [];
            this.selectedClientIds = [];
            this.selectedProjectesIds = [];
            this.getTimesheet({ employee_id: this.selectedEmployeeIds.value }, this.currentTabName.Employee, this.selectedEmployeeIds.label);
            break;
          }
        }
        default:
          this.getTimesheet({ defaultCall: true });
          this.errorInForm = false;
          this.selectedClientIds = [];
          this.selectedProjectesIds = [];
          this.selectedPositionOption = [];
          this.selectedEmployeeIds = [];
          break;
      }
    }
  }

  // todo further use case
  //  API for only position id
  // filterBYPosition(
  //   positionId: number,
  //   start_date: string,
  //   end_date: string
  // ): void {
  //   const params = new HttpParams()
  //     .set("start_date", start_date)
  //     .set("end_date", end_date);

  //   this.subscriptionManager.add(
  //     this.utilizationService.getPositionById(positionId, params).subscribe({
  //       next: (res) => {
  //         // console.log(res);
  //       },
  //     })
  //   );
  // }

  updateDateRange() {
    if (this.dataFilter.rollingOption) {
      const { startDate, endDate } = this.getStartDateEndDateFromRolling(this.dataFilter.rollingOption);
      this.dataFilter.util_start_date = this.datePipe.transform(startDate.toString(), AppConstants.format);
      this.dataFilter.util_end_date = this.datePipe.transform(endDate.toString(), AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.quarter = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (this.dataFilter.year) {
      const startEndDate = this.getStartEndDateFromYear(this.dataFilter.year);
      this.dataFilter.util_start_date = this.datePipe.transform(startEndDate.start_date.toString(), AppConstants.format);
      this.dataFilter.util_end_date = this.datePipe.transform(startEndDate.end_date.toString(), AppConstants.format);
      this.dataFilter.quarter = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
    if (this.dataFilter.quarter) {
      const startEndDate = this.getStartEndDateFromQuarter(this.dataFilter.quarter);
      this.dataFilter.util_start_date = this.datePipe.transform(startEndDate.start_date, AppConstants.format);
      this.dataFilter.util_end_date = this.datePipe.transform(startEndDate.end_date, AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
  }

  getStartEndDateFromYear(year) {
    return {
      start_date: new Date(year, 0, 1, 0, 0, 0, 0),
      end_date: new Date(year, 11, 31, 11, 59, 59, 999)
    };
  }

  getStartEndDateFromQuarter(quarter) {
    const now = new Date();
    const quarterDates = {
      Q1: {
        start_date: new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 2, 31, 11, 59, 59, 999)
      },
      Q2: {
        start_date: new Date(now.getFullYear(), 3, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 5, 30, 11, 59, 59, 999)
      },
      Q3: {
        start_date: new Date(now.getFullYear(), 6, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 8, 30, 11, 59, 59, 999)
      },
      Q4: {
        start_date: new Date(now.getFullYear(), 9, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 11, 31, 11, 59, 59, 999)
      }
    };
    return quarterDates[quarter];
  }

  getStartDateEndDateFromRolling(rollingOption: string) {
    const currentDate = new Date();
    const rollingMonths = parseInt(rollingOption.split(' ')[2]);

    const startDate = new Date(currentDate);
    startDate.setDate(1);

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + rollingMonths + 1, 0);

    return { startDate, endDate };
  }
  activeIndexTabChange(event: number) {
    this.currentTab = this.getEnumValueByIndex(CurrentTab, event || (0 as number));
    this.errorInForm = false;
  }
  getEnumValueByIndex(enumType: any, index: number): string {
    const enumValues = Object.values(enumType);
    return enumValues[index] as string;
  }

  getTimesheet(params?: any, currentTab?: string, label?: string): void {
    this.loadingTable = true;
    const param = {
      time_entry_start_date: this.datePipe.transform(this.startDate, AppConstants.format),
      time_entry_end_date: this.datePipe.transform(this.endDate, AppConstants.format),
      open_positions: false,
      visibility: !this.hasEmployeeEmail ? Visibility.Public : '',
      order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort()) : this.activeSort(),
      ...params
    };

    if (param['order_by']) {
      this.dataFilter.order_by = param['order_by'];
    } else {
      this.dataFilter.order_by = param['order_by'] = 'asc:name';
    }

    this.rememberChangesObjectCreation();
    this.customers = [];
    this.currentTabRecord = currentTab;
    this.updateFiltersTags(currentTab, label);

    this.mangeTimeService
      .getTimesheet(param)
      .pipe(
        map((response) => {
          if (response && response.data && response.data.positions) {
            return response?.data?.positions
              ?.filter((position) => {
                if (this.selectedTimeSheetHourStatus === TimesheetStatus.NotSubMitted && position?.position?.time_entries?.length > 0) {
                  return false;
                }
                return this.isStatusMatching(position, this.selectedTimeSheetHourStatus);
              })
              ?.map((position) => ({
                ...position,
                position: {
                  ...position.position,
                  time_entries: this.timeEntryFilter(position)
                }
              }));
          }
          return [];
        })
      )
      .subscribe({
        next: (res) => {
          if (res) {
            if (this.openFilter) {
              this.currentPage = this.appConstants.DEFAULT_PAGE;
              this.rowsPerPage = this.appConstants.DEFAULT_ROWS_PER_PAGE;
              this.rememberChanges();
              this.openFilter = false;
            }
            this.customers = res;
            this.updateTimesheetObj(res);
            this.totalHours = [];
            this.calculateTotalHours();
            this.isSubmitted = false;
            this.isDraft = false;
            res?.forEach((position) => {
              this.isSubmittedTimesheet(position?.position?.time_entries);
            });
            this.setExportHeaders(this.customers);
            this.selectedTimeSheetHourStatusHardCopy = this.selectedTimeSheetHourStatus;
            this.closeSideBar();
            this.sortColumnFlag = false;
          }
        },
        error: (err) => {
          this.loadingTable = false;
        },
        complete: () => {
          this.loadingTable = false;
        }
      });
  }

  updateTimesheetObj(positions: any): void {
    for (const position of positions) {
      const data = {};
      for (const time_entry of position?.position?.time_entries) {
        this.addKeyValue(data, ConvertDate(time_entry.time_entry.date), time_entry.time_entry.hours);
      }
      position.position = {
        ...position.position,
        ...data
      };
    }
  }

  addKeyValue(obj, key, value): void {
    obj[key] = value;
  }

  convertDateInDesireFormat(dateString: string): string {
    // sample  dateString = "2024-05-01";
    // Convert the string to a Date object
    const date = new Date(dateString);
    // Extract the day and month
    return `${date.getDate()} ${date.toLocaleString('default', {
      month: 'short'
    })}`;
  }

  getPreviousStartDate(): string {
    if (TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange || this.activatedMode === this.pageName.CreateTimeSheet) {
      this.startDate?.setDate(this.startDate?.getDate() - this.weekDays);
      this.endDate?.setDate(this.endDate?.getDate() - this.weekDays);
    } else if (TIMESHEET_RANGE.BIWEEKLY === this.selectedTimesheetRange) {
      this.startDate?.setDate(this.startDate?.getDate() - this.biweekDays);
      this.endDate?.setDate(this.endDate?.getDate() - this.biweekDays);
    }

    return this.datePipe.transform(this.startDate, AppConstants.format);
  }

  getNextStartDate(): string {
    if (TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange || this.activatedMode === this.pageName.CreateTimeSheet) {
      this.startDate?.setDate(this.startDate?.getDate() + this.weekDays);
      this.endDate?.setDate(this.endDate?.getDate() + this.weekDays);
    } else if (TIMESHEET_RANGE.BIWEEKLY === this.selectedTimesheetRange) {
      this.startDate?.setDate(this.startDate?.getDate() + this.biweekDays);
      this.endDate?.setDate(this.endDate?.getDate() + this.biweekDays);
    }

    return this.datePipe.transform(this.startDate, AppConstants.format);
  }

  getStartDate(): string {
    if (!this.startDate) {
      this.startDate = new Date(); //Month start date = this.monthArray[0].fullDate
    }

    return this.datePipe.transform(this.startDate, AppConstants.format);
  }

  onSelectColumsChange(event?) {
    if (event) {
      this.resizeFlag = false;
      this.loadingTable = true;
      this.columnToggle?.setSelectedColumns(event?.value, this.activatedMode === this.pageName.CreateTimeSheet ? PageNameData.CreateTimeSheet : PageNameData.MangeTimesheet);
      this._selectedColumns = event?.value;
      this._pCols = event?.value?.map((f) => f?.field);
      this.selectedColumns = event?.value;
      this.cdr.detectChanges();
    }
    this.setExportHeaders(this.customers);
    setTimeout(() => {
      this.resizeFlag = true;
      this.isShowHideColumns = !this.isShowHideColumns;
    }, 500);
    this.loadingTable = false;
    this.cdr.detectChanges();
  }

  getEndDate(): string {
    if (!this.endDate) {
      this.endDate = new Date(this.startDate);

      if (TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange) {
        this.endDate?.setDate(this.startDate?.getDate() + this.sixDays);
      } else if (TIMESHEET_RANGE.BIWEEKLY === this.selectedTimesheetRange) {
        this.endDate?.setDate(this.startDate?.getDate() + this.biweekDays);
      } else if (TIMESHEET_RANGE.MONTHLY === this.selectedTimesheetRange) {
        this.endDate = this.monthArray[this.monthArray?.length - 1]?.fullDate;
      }
    }

    return this.datePipe.transform(this.endDate, AppConstants.format);
  }

  prepareDataForSaveFilter(): void {
    this.prepareFilterState.currentDate = this.currentDate;
    this.prepareFilterState.currentTab = this.currentTab;
    this.prepareFilterState.selectedPostionOptions = this.selectedPositionOption;
    this.prepareFilterState.selectedClientIds = this.selectedClientIds;
    this.prepareFilterState.selectedProjectesIds = this.selectedProjectesIds;
    this.prepareFilterState.selectedEmployeeIds = this.selectedEmployeeIds;
  }

  getFilterDataAndUpdateBinding(): void {
    this.currentTab = this.prepareFilterState.currentTab;
    this.selectedPositionOption = this.prepareFilterState.selectedPostionOptions;
    this.selectedClientIds = this.prepareFilterState.selectedClientIds;
    this.selectedProjectesIds = this.prepareFilterState.selectedProjectesIds;
    this.selectedEmployeeIds = this.prepareFilterState.selectedEmployeeIds;
    const data = this.generateMonthArray();
    if (data) {
      this.filterApply();
    }
  }

  updateFiltersTags(selectedTab: string, selectedValueLabel: string): void {
    this.tags = [];
    if (this.getStartDate() && this.getEndDate()) {
      const dateTags = {
        label: 'Date Range',
        value: `${this.formatDate(this.getStartDate())} - ${this.formatDate(this.getEndDate())}`
      };
      this.tags.push(dateTags);
    }
    if (selectedTab && selectedValueLabel) {
      const SelectedTab = {
        label: selectedTab,
        value: selectedValueLabel
      };
      this.tags.push(SelectedTab);
    }
    if (this.selectedTimeSheetHourStatus) {
      const tagObject = {
        label: 'Timesheet Status',
        value: this.selectedTimeSheetHourStatus
      };

      this.tags.push(tagObject);
    }
  }

  resetFilterTags(): void {
    this.tags = [];
    this.currentDate = new Date();
    this.generateMonthArray();
    this.generateTimeRangeArray(this.selectedDate);
    this.resetDropDowns();
    this.customers = [];
  }

  resetDropDowns(): void {
    this.selectedClientIds = [];
    this.selectedProjectesIds = [];
    this.selectedEmployeeIds = [];
    this.selectedPositionOption = [];
  }
  formatDate(dateString: string): string {
    const date = new Date(dateString + this.appConstants.defaultTimePart);
    const year = date?.getFullYear();
    const month = String(date?.getMonth() + this.one)?.padStart(this.two, '0'); // Add leading zero for single-digit months
    const day = String(date?.getDate())?.padStart(this.two, '0');
    return `${month}/${day}/${year}`;
  }
  checkUserINSystem(userEmail: string): Promise<EmployeeLookupApiResponse> {
    return this.mangeTimeService.employeeLookup(userEmail).toPromise();
  }

  getUserEmail(): string {
    return localStorage.getItem('userEmail');
  }

  changePageMode(): void {
    this.activatedRoute.url.subscribe((data) => {
      if (PageName.MangeTimesheet === data[0].path) {
        this.activatedMode = PageName.MangeTimesheet;
      } else {
        this.activatedMode = PageName.CreateTimeSheet;
      }
      this.columnList();
    });
  }

  async createTimesheetFlow(): Promise<void> {
    this.currentTab = this.currentTabName.Employee;
    const userEmail = this.getUserEmail();
    if (userEmail) {
      const userData = await this.checkUserINSystem(userEmail);
      this.employeeData = userData?.data?.employees ? userData.data.employees : [];
      if (userData?.data?.employees && userData?.data?.employees[0]?.employee.id && userData?.data?.employees[0]?.employee?.name) {
        this.selectedEmployeeIds = {
          label: userData?.data?.employees[0]?.employee?.name,
          value: userData.data.employees[0].employee.id
        };
        this.getWorkExceptionBYEmployee(this.selectedEmployeeIds.value);
        this.filterApply();
      }
    }
  }

  columnList(): void {
    this.frozenCols = [
      {
        field: 'Client',
        monthLabel: 'Client',
        sort: true,
        isFrozenColumn: true,
        cssClass: 'skill-set-wrapper'
      },
      {
        field: 'Employee',
        monthLabel: 'Employee',
        sort: true,
        isFrozenColumn: true,
        cssClass: 'skill-set-wrapper'
      },
      {
        field: 'Daily value',
        monthLabel: 'Daily value',
        sort: false,
        isFrozenColumn: false
      },
      {
        field: 'Total',
        monthLabel: 'Total',
        sort: false,
        isFrozenColumn: true
      }
    ];

    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      this._selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.createTimesheet
        ? JSON.parse(localStorage.getItem('selectedColumnsArray'))[PageNameData.CreateTimeSheet]
        : this.frozenCols.filter((col) => !this.notIncludeCreateTimesheet.includes(col.field));
    } else {
      this._selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.manageTimeSheet
        ? JSON.parse(localStorage.getItem('selectedColumnsArray'))[PageNameData.MangeTimesheet]
        : this.frozenCols;
    }

    this.selectedColumns = this._selectedColumns;
    this._pCols = this.selectedColumns.map((f) => f.field);
    if (this.activatedMode === this.pageName.MangeTimesheet) {
      this.frozenCols = [
        ...this.frozenCols,
        {
          field: 'status',
          monthLabel: 'Status',
          sort: false,
          isFrozenColumn: false
        }
      ];
    }
  }
  checkerEmployeeID(): Promise<any> {
    return this.activatedRoute.queryParams.toPromise();
  }

  mangeTimeFlow(openSidebar?: boolean): void {
    this.activatedRoute.queryParams.subscribe((employee) => {
      if (employee?.email) {
        this.activeIndexTab = 3;
        if (openSidebar) {
          this.openSideBar();
        }
        this.getFilterBYEmail(employee.email);
        this.cdr.detectChanges();
        if (this.activatedMode === this.pageName.CreateTimeSheet) {
          this.hasEmployeeEmail = true;
        }
      } else if (employee?.filterId && this.activatedMode === this.pageName.MangeTimesheet) {
        return;
      } else {
        this.openFilter = openSidebar;
        this.openSidebarFlow();
      }
    });
  }
  async getFilterBYEmail(userEmail: string): Promise<void> {
    const userData = await this.checkUserINSystem(userEmail);
    if (userData?.data?.employees && userData?.data?.employees[0]?.employee.id && userData?.data?.employees[0]?.employee?.name) {
      this.selectedEmployeeIds = {
        label: userData?.data?.employees[0]?.employee?.name,
        value: userData.data.employees[0].employee.id.toString()
      };
      this.filterApply();
    }
  }
  openSidebarFlow() {
    if (this.sidebarFilter && this.activatedMode === this.pageName.MangeTimesheet) {
      this.checkOpenFilterBased();
    }
  }

  checkOpenFilterBased(): void {
    const filter = this.getRememberChanges();
    this.openFilter ? this.openSideBar() : filter ? this.rememberApplyChanges() : this.defaultAllDataFilter();
  }

  defaultAllDataFilter(): void {
    this.currentTab = this.currentTabName.Client;
    this.selectedClientIds = [];
    this.filterApply();
    this.cdr.detectChanges();
  }

  isDateInRange(dateStr: Date, startDateStr: string, endDateStr: string): boolean {
    // Parse the dates into Date objects
    // const date = new Date(dateStr);
    // remove timezone dependency issue
    const date = new Date(this.formatDateInString(dateStr));
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    // Check if the date is greater than or equal to the start date
    // and less than or equal to the end date
    return date >= startDate && date <= endDate ? true : false;
  }
  formatDateInString(date: Date): string {
    const year = date?.getFullYear();
    const month = String(date?.getMonth() + this.one)?.padStart(this.two, '0'); // Add leading zero for single-digit months
    const day = String(date?.getDate())?.padStart(this.two, '0'); // Add leading zero for single-digit days
    return `${year}-${month}-${day}`;
  }

  positionNOTInRangePopup(): void {
    this.positionNOTInRange = true;
  }

  closePositionModal(): void {
    this.positionNOTInRange = false;
  }

  openExportOptionList(): void {
    this.showExportOptionDialog = true;
    this.showExportOptions = true;
  }

  exportReport(type): void {
    const startDate = this.formatDate(this.getStartDate());
    const endDate = this.formatDate(this.getEndDate());

    if (type === 'csv') {
      this.utilizationService.exportToCsv(this.exportReportData, this.appConstants.ManageTimesheetFileName, this.csvCols, startDate, endDate);
    }
    if (type === 'excel') {
      this.utilizationService.exportExcelWithComments(
        this.excelHeaders,
        this.excelExportReportData,
        this.appConstants.ManageTimesheetFileName,
        [],
        this.exportComments,
        startDate,
        endDate
      );
    }
    if (type === 'pdf') {
      if (this.exportPdfColumns?.length > 38) {
        this.unableToDownloadPdf = true;
      } else {
        this.utilizationService.exportPdfManageTimesheet(this.exportPdfColumns, this.exportReportData, this.appConstants.ManageTimesheetFileName, 12, startDate, endDate);
      }
    }
  }

  setExportHeaders(customer): void {
    this.exportCopyMonth = [];
    this.exportPdfColumns = [];
    this.exportComments = [];
    if (this.checkSelectedColumnDynamic('client', this.selectedColumns)) {
      this.exportPdfColumns.push({ title: 'Client', dataKey: 'client' });
    }

    this.exportPdfColumns = [{ title: 'Project', dataKey: 'project' }, { title: 'Position', dataKey: 'postion' }, ...this.addDynamicColumn(['total'])];

    if (this.checkSelectedColumnDynamic('employee', this.selectedColumns)) {
      this.exportPdfColumns.push({ title: 'Employee', dataKey: 'employee' });
    }

    this.dateColumns = [];
    if (this.checkSelectedColumnDynamic('Daily value', this.selectedColumns)) {
      this.monthArrayExport =
        this.selectedTimesheetRange === TIMESHEET_RANGE.MONTHLY ? this.monthArray : this.selectedTimesheetRange === TIMESHEET_RANGE.WEEKLY ? this.weekArray : this.biWeekArray;

      this.dateColumns = this.monthArrayExport
        .map((item) => {
          if (item?.fullDate && item?.label) {
            return {
              title: `${this.getWeekdayName(item.fullDate)} ${item.label}`,
              dataKey: `${this.getWeekdayName(item.fullDate)} ${item.label}`
            };
          }
        })
        .filter((item) => item);
    }

    const afterCalenderColumn = [];

    if (this.checkSelectedColumnDynamic('Total', this.selectedColumns)) {
      afterCalenderColumn.push({ title: 'Total', dataKey: 'total' });
    }

    this.afterCalenderColumnGlobal = afterCalenderColumn;

    this.exportPdfColumns = [...this.exportPdfColumns, ...this.dateColumns, ...afterCalenderColumn?.filter((item) => item)];
    this.exportCopyMonth = [...this.dateColumns];

    const index = this.exportPdfColumns.findIndex((data) => data.title === 'Daily value');
    index !== -1 ? this.exportPdfColumns.splice(index, 1) : '';

    this.csvCols = this.exportPdfColumns.map((col) => col.dataKey);

    this.excelHeaders = [
      this.exportPdfColumns.reduce((acc, item) => {
        acc[item.dataKey] = item.title;
        return acc;
      }, {})
    ];

    const headerComment = Object.keys(this.excelHeaders[0]).fill(null);
    this.exportComments.push(headerComment);

    this.prepareDataForExpert(customer);
  }

  prepareDataForExpert(customer: any): void {
    let prepareData = [];
    let comment = [];
    for (const [index, postion] of customer?.entries()) {
      let exportData = {};
      // exportData['position_id']=postion?.position?.pid;
      if (this.checkSelectedColumn('client')) {
        exportData['client'] = postion?.position?.project?.customer?.name || '';
      }
      exportData = {
        ...exportData,
        project: postion?.position?.project?.name || '',
        postion: postion?.position?.name || ''
      };
      if (this.checkSelectedColumn('employee')) {
        exportData['employee'] = `${postion?.position?.employee?.first_name || ''} ${postion?.position?.employee?.last_name || ''}`;
      }

      if (this.checkSelectedColumn('status')) {
        exportData['status'] = postion?.position?.time_entries ? this.includeStatusArray(postion?.position?.time_entries) : '';
      }

      let comments = [];
      let dateColumns = {};

      if (this.checkSelectedColumnDynamic('Daily value', this.selectedColumns)) {
        const nullArray = new Array(this.exportComments[0]?.length - this.monthArrayExport?.length - (this.afterCalenderColumnGlobal.length || 0)).fill(null);

        comments = [...nullArray];

        for (const item of this.monthArrayExport) {
          if (item?.fullDate && item?.label) {
            dateColumns[`${this.getWeekdayName(item.fullDate) + ' ' + item.label}`] = this.getHoursForDate(postion?.position?.time_entries, item?.fullDate) || '';
            comments.push(this.getCommentByPositionAndDate(postion?.position?.time_entries, item.fullDate));
          }
        }
      }
      if (this._pCols.includes('Total')) {
        exportData['total'] = (this.totalHours[index] || 0)?.toFixed(2);
        comments.push(null);
      }
      let newFinalValue = { ...exportData, ...dateColumns };
      this.exportComments.push(comments);
      prepareData.push(this.addExtendDetails(newFinalValue, postion));
    }
    this.exportReportData = prepareData;
    this.excelExportReportData = this.exportReportData;
  }
  filterExcelDataWithSelectedColums(): void {
    this.excelExportReportData = this.excelExportReportData.map((res) => {
      const filteredData = {};
      for (const key in this.excelHeaders[0]) {
        if (res[key]) {
          filteredData[key] = res[key];
        }
      }
      return filteredData;
    });
  }

  getWeekdayName(date: Date): string {
    return date ? date.toLocaleDateString('en-US', { weekday: 'short' }) : '';
  }

  addDynamicColumn(notInclude?: Array<string>): Array<any> {
    return (this.selectedColumnExport = this.selectedColumns
      .map((data) => {
        if (!notInclude?.includes(data.field.toLowerCase())) {
          return {
            title: data.monthLabel,
            dataKey: data.field.toLowerCase()
          };
        }
        return null;
      })
      .filter(Boolean));
  }

  checkSelectedColumn(dataKey: string): boolean {
    return this.selectedColumnExport.some((data) => data.dataKey === dataKey);
  }

  checkSelectedColumnDynamic(dataKey: string, selectedColumnExport: Array<any>): boolean {
    return selectedColumnExport.some((data) => data.field === dataKey);
  }

  getHoursForDate(timeEntries, dateObj): number | string {
    // Convert the target date to UTC
    const targetDateUTC = new Date(dateObj.getTime() - dateObj.getTimezoneOffset() * 60000);
    const targetDateString = targetDateUTC.toISOString().split('T')[0];

    const matchingEntry = timeEntries?.find((entry) => {
      // Convert the JSON date to UTC
      const entryDateUTC = new Date(entry?.time_entry?.date)?.toISOString()?.split('T')[0];
      return entryDateUTC === targetDateString;
    });
    return matchingEntry ? parseFloat(matchingEntry?.time_entry?.hours)?.toFixed(2) : 0;
  }

  generateTimeRangeArray(inputDate: Date): void {
    if (
      TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange ||
      TIMESHEET_RANGE.BIWEEKLY === this.selectedTimesheetRange ||
      this.activatedMode === this.pageName?.CreateTimeSheet
    ) {
      const selectedRangeResult = this.generateDateRanges(inputDate, this.selectedTimesheetRange);
      const length = selectedRangeResult?.selectedRange?.length;
      if (selectedRangeResult && length) {
        this.startDate = new Date(selectedRangeResult?.selectedRange[0] + this.appConstants.defaultTimePart);
        this.endDate = new Date(selectedRangeResult?.selectedRange[length - 1] + this.appConstants.defaultTimePart);
      }
      const selectedRange = selectedRangeResult?.selectedRange?.map((date) => ({
        label: `${new Date(date + this.appConstants.defaultTimePart)?.getDate()} ${this.getMonthAbbreviation(new Date(date + this.appConstants.defaultTimePart)?.getMonth())}`,
        fullDate: new Date(date + this.appConstants.defaultTimePart)
      }));

      TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange ? (this.weekArray = selectedRange) : (this.biWeekArray = selectedRange);
    }

    this.totalHours = [];
    this.calculateTotalHours();
    this.selectedDateRange = [this.startDate, this.endDate];
  }

  onSelectDate(event?: any): void {
    this.selectedDate = this.selectedDateRange[0];
    this.generateTimeRangeArray(this.selectedDate);
    this.selectedDateRange = [this.startDate, this.endDate];
    this.calendar?.hideOverlay();
    this.filterApply();
  }

  generateDateRanges(selectedDate, returnType = TIMESHEET_RANGE.WEEKLY) {
    if (![TIMESHEET_RANGE.WEEKLY, TIMESHEET_RANGE.BIWEEKLY]?.includes(returnType)) {
      return;
    }

    this.prevSelectedTimeRange = returnType;
    let daysInRange = returnType === TIMESHEET_RANGE.WEEKLY ? this.weekDays * this.totalWeeks : this.biWeekRange;

    const selected = this.formatDateToTimezone(selectedDate);
    const compareDate = this.formatDateToTimezone(selectedDate);

    const start = new Date(selected);

    if (returnType === TIMESHEET_RANGE.BIWEEKLY) {
      if (selected?.getDate() <= this.biWeekRange) {
        start?.setDate(1);
      } else {
        start?.setDate(this.biWeekRange + 1);
        const monthEndDate = new Date(start?.getFullYear(), start?.getMonth() + 1, 0);
        daysInRange = monthEndDate?.getDate() - this.biWeekRange;
      }
    } else {
      if (selected?.getDay() < this.startDayValue) {
        start?.setDate(selected?.getDate() - selected?.getDay() - this.weekDays + this.startDayValue);
      } else {
        start?.setDate(selected?.getDate() - selected?.getDay() + this.startDayValue);
      }
    }

    const ranges = [];
    let currentStart = new Date(start);

    for (let i = 0; i < 10; i++) {
      const rangeDays = [];
      for (let j = 0; j < daysInRange; j++) {
        const currentDate = new Date(currentStart);
        currentDate?.setDate(currentStart?.getDate() + j);
        rangeDays.push(this.formattedDateForRange(currentDate));
      }
      ranges.push(rangeDays);
      currentStart?.setDate(currentStart?.getDate() + daysInRange);
    }

    const selectedRange = ranges?.find((range) => {
      const rangeStart = new Date(this.datePipe?.transform(range[0], AppConstants.format));
      const rangeEnd = new Date(this.datePipe?.transform(range[range?.length - 1], AppConstants.format));
      return new Date(this.datePipe?.transform(compareDate, AppConstants.format)) >= rangeStart && new Date(this.datePipe?.transform(compareDate, AppConstants.format)) <= rangeEnd;
    });

    return { ranges, selectedRange };
  }

  formattedDateForRange(currentDate: Date) {
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const day = String(currentDate.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  formatDateToTimezone(date: Date, timezone = null): Date {
    if (!timezone) {
      timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    }

    return new Date(new Date(date).toLocaleString('en-US', { timeZone: timezone }));
  }

  onAddCommentClick(event: MouseEvent, day: any, i: number, hours = 0, timeSheets?: any[], hoverComment = false): void {
    event.stopPropagation();
    this.addCommentText = this.getComment(day, timeSheets, i) ?? null;
    this.editComment = {} as EditComment;
    this.editComment.index = i;
    this.editComment.day = day?.fullDate;
    this.editComment.hours = hours;
    this.isDisableComment = this.isEditableHoursComment(timeSheets, this.editComment.day);
    if (this.op) {
      this.op.show(event);
    }
    this.isHoverComment = hoverComment;
    this.isClickedToEditComment = !hoverComment;
    this.cdr.detectChanges();
  }

  getComment(day: any, timeSheets?: any[], index?: number): string {
    const test = timeSheets?.find((entry) => entry?.time_entry?.date === moment(day?.fullDate).format(this.appConstants.dateFormate_YYYY_MM_DD));

    const indexForAlredayAddPositionHours = this.userAddORUpdatedTime.findIndex(
      (res) => res.position_id === this.customers[index]?.position?.id && res.date === moment(day?.fullDate).format(this.appConstants.dateFormate_YYYY_MM_DD)
    );

    return test?.time_entry?.comment ? test?.time_entry?.comment : this.userAddORUpdatedTime[indexForAlredayAddPositionHours]?.comment;
  }

  saveComment(): void {
    const index = this.editComment?.index;
    this.editComment.comment = this.addCommentText;
    this.validateNumberInput('', this.editComment?.day, this.editComment?.index, this.editComment?.comment, this.editComment?.hours);
    this.op?.hide();
    this.isDisableComment = false;
    this.updateObjBeforeSave(index);
    this.isHoverComment = false;
    this.isClickedToEditComment = false;
  }

  cancelComment(): void {
    this.op?.hide();
    const overlayElement = document.querySelector('.p-overlaypanel');
    if (overlayElement) {
      overlayElement.remove();
    }
    this.editComment = {} as EditComment;
    this.isDisableComment = false;
    this.isHoverComment = false;
    this.isClickedToEditComment = false;
    this.cdr.detectChanges();
  }

  getGlobalDetailsCategory(): void {
    this.subscriptionManager.add(
      this.adminService.getExtendedFields('ManageExtendedFiled').subscribe((res) => {
        if (res?.data?.global_details) {
          const globalDetail = res?.data?.global_details;
          if (globalDetail && globalDetail[0]?.global_detail?.name === 'ManageExtendedFiled') {
            this.extendFields = res.data.global_details[0].global_detail.extended_fields.extendArray || [];
            if (this.activatedMode === PageName.MangeTimesheet) {
              this.frozenCols = [
                ...this.frozenCols,
                ...this.extractExtendFlow(ComponentsType.Position),
                ...this.extractExtendFlow(ComponentsType.Client),
                ...this.extractExtendFlow(ComponentsType.Employee),
                ...this.extractExtendFlow(ComponentsType.Project)
              ];
            }
          }
        }
      })
    );
  }

  checkSelectedColumnExtendFiled(componentType: ComponentsType, key: string): boolean {
    const finalKey = `${componentType}: ${key?.replace(/\s/g, '_')?.trim()}`.trim() || '';
    return this._pCols.includes(finalKey);
  }

  extractNames(data: any, componentName: string): string[] {
    return data
      ?.map((item) => {
        const projectConfig = item?.jsonData?.extendedFieldsConfig?.find((config) => config?.component === componentName);
        return projectConfig ? projectConfig?.fields.map((field) => field?.name?.trim()) : [];
      })
      .flat(2);
  }

  extractExtendFlow(componentType: ComponentsType): Array<any> {
    let extendFiled = [];
    let prepareKey = this.extractNames(this.extendFields, componentType);
    for (const key of prepareKey) {
      const keyPreProcess = `${componentType}: ${key?.replace(/\s/g, '_').trim()}`.trim();
      extendFiled.push({
        dataKey: keyPreProcess || '',
        title: `${componentType}: ${key?.toUpperCase()}`,
        monthLabel: `${componentType}: ${key?.toUpperCase()}`,
        field: keyPreProcess,
        bydefaultSelected: false
      });
    }
    return extendFiled;
  }

  getValueBYExtendFiled(componentsType: ComponentsType, object: OpenPositionReport, dbTag: string, fieldType?: string): string {
    let extendedOBJ = {};
    switch (componentsType) {
      case ComponentsType.Position:
        extendedOBJ = object?.position?.extended_fields || {};
        break;
      case ComponentsType.Project:
        extendedOBJ = object?.position?.project?.extended_fields || {};
        break;
      case ComponentsType.Client:
        extendedOBJ = object?.position?.project?.customer?.extended_fields || {};
        break;
      case ComponentsType.Employee:
        extendedOBJ = object?.position?.employee?.extended_fields || {};
        break;
    }
    return this.getValueByDBTag(dbTag, extendedOBJ, fieldType);
  }

  getValueByDBTag(dbTag: string, extendFieldsObj: any = {}, fieldType?: string): string {
    if (fieldType === this.filedType.MultiDropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      const value = Array.isArray(extendFieldsObj[dbTag]) ? extendFieldsObj[dbTag]?.map((item) => item?.name).join(', ') : '';
      return value;
    }
    if (fieldType === this.filedType.Dropdown && extendFieldsObj?.hasOwnProperty(dbTag)) {
      return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag].name : '';
    }
    return extendFieldsObj?.hasOwnProperty(dbTag) ? extendFieldsObj[dbTag] : '';
  }

  addExtendDetails(existingObj, customers): any {
    const mergedObject = this.processObj(customers)?.reduce((acc, obj) => ({ ...acc, ...obj }), existingObj);
    return mergedObject;
  }

  processObj(data: any): any {
    let final = this.extendFields
      ?.map((item) => {
        return item?.jsonData?.extendedFieldsConfig.map((filed) => {
          return filed?.fields.map((filedDetails) => {
            let dateColumns = {};
            const filedName = `${filed?.component}: ${filedDetails?.name?.replace(/\s/g, '_')?.trim()}`?.trim();
            if (this._pCols.includes(filedName)) {
              dateColumns[filedName?.toLowerCase()] = this.getValueBYExtendFiled(filed?.component, data, filedDetails?.DBTag);
            }
            return { ...dateColumns };
          });
        });
      })
      ?.flat();
    return final?.flat();
  }

  closePdfModel(): void {
    this.unableToDownloadPdf = false;
    this.showExportOptionDialog = false;
    this.showExportOptions = false;
  }

  calculateTotalHours(): void {
    const timeRange =
      this.selectedTimesheetRange === TIMESHEET_RANGE.WEEKLY ? this.weekArray : this.selectedTimesheetRange === TIMESHEET_RANGE.BIWEEKLY ? this.biWeekArray : this.monthArray;
    let index = 0;
    this.customers?.forEach((customer) => {
      timeRange?.forEach((day) => {
        if (this.activatedMode === this.pageName.CreateTimeSheet && this.getTimeEntryPrePopulateValue(day, customer) !== null) {
          customer.position[day.label] = this.getTimeEntryPrePopulateValue(day, customer);
        }
        if (this.isDateInRange(day?.fullDate, customer?.position?.start_date, customer?.position?.end_date) && parseFloat(customer?.position[day?.label])) {
          this.totalHours[index] ? (this.totalHours[index] += parseFloat(customer?.position[day?.label])) : (this.totalHours[index] = parseFloat(customer?.position[day?.label]));
        }
      });
      index += 1;
    });
  }

  isWorkException(day: TimeSheetDayObj, customer: Position): boolean {
    const workExceptions = this.workExceptionsData;
    const formatDate = formatDateToYYYYMMDD(new Date(day.fullDate));
    const match = workExceptions?.find((exception) => {
      return exception?.work_exception?.date === formatDate && exception?.work_exception?.employee.id === customer?.position?.employee?.id;
    });
    return !!match;
  }

  getTimeEntryPrePopulateValue(day: TimeSheetDayObj, customer: Position): string | number | null {
    if (this.isDateInRange(new Date(day?.fullDate), customer?.position?.start_date, customer?.position?.end_date) && customer?.position[day?.label]) {
      return customer?.position[day?.label];
    }
    if (
      this.isDateInRange(new Date(day?.fullDate), customer?.position?.start_date, customer?.position?.end_date) &&
      !customer?.position[day?.label] &&
      !this.isWeekend(new Date(day?.fullDate)) &&
      !this.isWorkException(day, customer) &&
      new Date(day.fullDate) <= new Date()
    ) {
      const addedTime = {
        date: moment(day.fullDate).format(this.appConstants.dateFormate_YYYY_MM_DD),
        hours: customer?.position?.daily_billable_hours,
        position_id: customer?.position?.id,
        comment: null,
        status: TimesheetStatus.Draft
      };
      const isAdded = customer.position.time_entries.findIndex(
        (timeEntry) => timeEntry.time_entry.date === addedTime.date && timeEntry.time_entry.position_id === addedTime.position_id
      );
      if (isAdded === -1) {
        customer.position.time_entries.push({ time_entry: addedTime });
      }
      return customer?.position?.daily_billable_hours;
    }
    return null;
  }

  getGlobalDetail(): void {
    this.loadingTable = true;
    this.subscriptionManager.add(
      this.adminService.getGlobalDetailsFields().subscribe({
        next: (res) => {
          if (res?.data?.global_details) {
            res?.data?.global_details?.forEach((field) => {
              const timesheetConfig = field?.global_detail?.extended_fields?.timesheetConfig;
              if (timesheetConfig) {
                this.selectedTimesheetRange = timesheetConfig?.frequency;
                if (this.selectedTimesheetRange === TIMESHEET_RANGE.WEEKLY) {
                  const startDay = timesheetConfig?.startDay?.toUpperCase();
                  this.startDayValue = VALUE_OF_WEEK_DAYS[startDay];
                  this.totalWeeks = timesheetConfig?.weeks;
                } else if (this.selectedTimesheetRange === TIMESHEET_RANGE.BIWEEKLY && timesheetConfig.startDaySecondPeriod) {
                  if (this.activatedMode === this.pageName.CreateTimeSheet) {
                    this.activatedRoute.queryParams.subscribe((params) => {
                      params['date'] ? (this.selectedDate = this.currentDate = new Date(params['date'])) : '';
                    });
                    timesheetConfig.startDaySecondPeriod = this.selectedDate.getDate() > this.biWeekRange ? this.biWeekRange + 1 : 1;
                  }
                  timesheetConfig.startDaySecondPeriod > this.biWeekRange ? this.selectedDate.setDate(this.biWeekRange + 1) : this.selectedDate.setDate(1);
                }
              }
              this.generateMonthArray();
              this.generateTimeRangeArray(this.selectedDate);
              this.loadingTable = false;
            });
          }
        },
        error: () => {
          this.loadingTable = false;
        },
        complete: () => {
          this.mangeTimeFlow(false);
          this.activatedMode === this.pageName.CreateTimeSheet ? this.createTimesheetFlow() : '';
        }
      })
    );
  }

  getCommentByPositionAndDate(timeEntries, dateObj: Date): string | null {
    if (!timeEntries) {
      return null; // Return null if the position object is invalid or has no time_entries
    }

    const targetDateUTC = new Date(dateObj.getTime() - dateObj.getTimezoneOffset() * 60000);
    const targetDateString = targetDateUTC.toISOString().split('T')[0];

    const matchingEntry = timeEntries.find((entry) => {
      // Convert the JSON date to UTC
      const entryDateUTC = new Date(entry.time_entry.date).toISOString().split('T')[0];
      return entryDateUTC === targetDateString;
    });

    return matchingEntry ? matchingEntry?.time_entry?.comment || null : null;
  }

  getExceptionTypes(): void {
    this.exceptionTypes = [];
    this.loadingTable = true;
    this.cdr.detectChanges();
    this.subscriptionManager.add(
      this.adminService.getExceptionTypes().subscribe(
        (res) => {
          this.loadingTable = false;
          if (res?.data?.work_exception_types) {
            this.exceptionTypes = res.data.work_exception_types;
          }
          this.cdr.detectChanges();
        },
        () => (this.loadingTable = false)
      )
    );
  }

  getWorkExceptionBYEmployee(employeeId: number): void {
    const queryFilter = {
      employee_ids: employeeId
    };
    this.loadingTable = true;
    this.subscriptionManager.add(
      this.adminService.getManageWorkExceptions(queryFilter).subscribe(
        (res) => {
          this.workExceptionsData = res?.body?.data?.work_exceptions;
          this.loadingTable = false;
          this.cdr.detectChanges();
        },
        () => (this.loadingTable = false)
      )
    );
  }

  getHoursByIdAndDate(id, date: Date): number | null {
    const workExceptions = this.workExceptionsData;
    const formatDate = formatDateToYYYYMMDD(date);

    // const match = workExceptions?.find(exception => { return exception?.work_exception?.work_exception_type?.id === id && exception?.work_exception?.date === formatDate }
    // );
    const match = workExceptions?.filter((exception) => {
      return exception?.work_exception?.work_exception_type?.id === id && exception?.work_exception?.date === formatDate;
    });

    let total = 0;

    for (const element of match || []) {
      total = element.work_exception?.hours ? element.work_exception?.hours + total : null;
    }
    return total ? total : null;
  }

  closeBookTimeOffModel(value?: boolean): void {
    this.bookTimeOffPopUp = false;
    if (value) {
      this.getWorkExceptionBYEmployee(this.selectedEmployeeIds.value);
    }
    this.bookTimeOff.resetForm();
  }

  saveBookTimeOffAPI(): void {
    this.bookTimeOff?.onSave();
  }

  checkWhenEditableMode(): boolean {
    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      return true;
    } else {
      // return  this.selectedTimeSheetHourStatus?  (this.selectedTimeSheetHourStatus==TimesheetStatus.ALL || this.selectedTimeSheetHourStatus == TimesheetStatus.APPROVED ? true : false) : true;
      return true;
    }
  }

  timeEntryFilter(position): Array<any> {
    if (this.selectedTimeSheetHourStatus && this.selectedTimeSheetHourStatus.toLowerCase() != TimesheetStatus.ALL.toLowerCase()) {
      const process = position?.position?.time_entries?.filter((entry) => entry?.time_entry?.status === this.selectedTimeSheetHourStatus);

      return process ? process : [];
    } else {
      return position?.position?.time_entries;
    }
  }

  filterSaveProcess(selectedTab: string, save?: boolean, value?: any): any {
    switch (selectedTab) {
      case this.currentTabName.Position: {
        if (save && this.selectedPositionOption?.value) {
          return this.selectedPositionOption;
        } else if (!save && value) {
          this.selectedPositionOption = value;
        }
        this.activeIndexTab = 2;
        break;
      }
      case this.currentTabName.Client: {
        if (save && this.selectedClientIds?.value) {
          return this.selectedClientIds;
        } else if (!save && value) {
          this.selectedClientIds = value;
        }
        this.activeIndexTab = 0;
        break;
      }
      case this.currentTabName.Project: {
        if (save && this.selectedProjectesIds?.value) {
          return this.selectedProjectesIds;
        } else if (!save && value) {
          this.selectedProjectesIds = value;
        }
        this.activeIndexTab = 1;
        break;
      }
      case this.currentTabName.Employee: {
        if (save && this.selectedEmployeeIds?.value) {
          return this.selectedEmployeeIds;
        } else if (!save && value) {
          this.selectedEmployeeIds = value;
          this.activeIndexTab = 3;
        }
        break;
      }
    }
  }

  isSubmittedTimesheet(time_entries: TimeEntry[]): void {
    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      const isSubmittedTimeEntry = time_entries?.some((time_entry) => {
        return time_entry?.time_entry?.status === TimesheetStatus.SUBMITTED;
      });
      const isDraftTimeEntry = time_entries?.some((time_entry) => {
        return time_entry?.time_entry?.status === TimesheetStatus.Draft;
      });
      this.isSubmitted = this.isSubmitted || isSubmittedTimeEntry;
      this.isDraft = this.isDraft || isDraftTimeEntry;
    }
  }

  isEditableHoursComment(time_entries: TimeEntry[], date: Date | string): boolean {
    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      const dayDate = this.datePipe.transform(date, this.appConstants.format);
      const isEditable = time_entries?.some((time_entry) => {
        const timeSheetDate = this.datePipe.transform(time_entry?.time_entry?.date, this.appConstants.format);
        return dayDate === timeSheetDate && time_entry?.time_entry?.status === TimesheetStatus.APPROVED;
      });
      return isEditable || this.isSubmitted;
    } else {
      return !this.isPermissionManageTime;
    }
  }

  timeentryStatus(time_entries: TimeEntry[], date: Date | string): string {
    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      const dayDate = this.datePipe.transform(date, this.appConstants.format);
      const timeEntry = time_entries.find((time_entry) => {
        const timeSheetDate = this.datePipe.transform(time_entry?.time_entry?.date, this.appConstants.format);
        return dayDate === timeSheetDate;
      });
      return timeEntry ? timeEntry?.time_entry?.status || '' : '';
    }
    return '';
  }

  checkStatus(entries: any, timesheetStatus: string = TimesheetStatus.APPROVED): boolean {
    if (!entries) {
      return false;
    } else if (this.checkEveryStatusSame(entries, TimesheetStatus.APPROVED)) {
      return false;
    } else if (this.checkEveryStatusSame(entries, TimesheetStatus.Rejected)) {
      return false;
    } else {
      return true;
    }
  }

  openApprovedRejectedFlow(entries: TimeEntry[], isApproved: boolean, position: Position): void {
    if (
      (this.checkEveryStatusSame(entries, this.timeSheetStatusEnum.APPROVED) && isApproved) ||
      (this.checkEveryStatusSame(entries, this.timeSheetStatusEnum.Rejected) && !isApproved)
    ) {
      position.position.edit = false;
      return;
    }
    this.showApprovedDialog = !isApproved;
    this.isApproved = isApproved;
    this.recordData = entries;
    if (isApproved) {
      this.updateHourSApproved();
    }
  }

  updateHourSApproved(): void {
    const match = this.isApproved ? TimesheetStatus.APPROVED : TimesheetStatus.Rejected;
    this.userAddORUpdatedTime = this.recordData
      ?.filter((data) => data?.time_entry?.status !== match)
      ?.map((time_entry) => {
        return {
          ...{ ...time_entry?.time_entry, status: this.isApproved ? TimesheetStatus.APPROVED : TimesheetStatus.Rejected }
        };
      });
    if (!this.userAddORUpdatedTime?.length) {
      this.closeApprovePopup();
    }
    this.saveData(true);
  }

  closeApprovePopup(): void {
    this.showApprovedDialog = false;
  }

  checkEveryStatusSame(timeEntries, timesheetStatus: string): boolean {
    return timeEntries.every((entry) => entry?.time_entry?.status === timesheetStatus);
  }

  outputDayHours(customer: any, day: TimeSheetDayObj): string {
    const workExceptions = this.workExceptionsData;
    const formatDate = formatDateToYYYYMMDD(new Date(day.fullDate));
    const match = workExceptions?.find((exception) => {
      return exception?.work_exception?.date === formatDate && exception?.work_exception?.employee.id === customer?.position?.employee?.id;
    });
    if (new Date(day.fullDate) > new Date() || this.isWeekend(new Date(day.fullDate)) || match || this.activatedMode === this.pageName.MangeTimesheet) {
      return customer?.position[day?.label] !== undefined ? (+customer?.position[day?.label])?.toFixed(2) : '';
    }
    return customer?.position[day?.label] !== undefined ? (+customer?.position[day?.label])?.toFixed(2) : customer?.position?.daily_billable_hours || '';
  }

  includeStatusArray(timeEntries): string {
    const status = [];
    for (const element of timeEntries) {
      element.status;
      if (element?.time_entry?.status) {
        if (!status.includes(element?.time_entry?.status)) {
          status.push(element.time_entry.status);
        }
      } else if (status?.length === 3) {
        break;
      }
    }

    if (status.includes(TimesheetStatus.Draft)) {
      return TimesheetStatus.Draft;
    } else if (status.includes(TimesheetStatus.SUBMITTED)) {
      return TimesheetStatus.SUBMITTED as string;
    } else if (status.includes(TimesheetStatus.Rejected)) {
      return TimesheetStatus.Rejected as string;
    } else if (status.includes(TimesheetStatus.APPROVED)) {
      return TimesheetStatus.APPROVED as string;
    } else {
      return '';
    }
  }

  checkAnyStatusAssigned(timeEntries, timesheetStatus): boolean {
    return timeEntries.some((entry) => entry?.time_entry?.status === timesheetStatus);
  }

  isStatusMatching(position, selectedTimeSheetHourStatus): boolean {
    // Check if the activated mode is not 'ManageTimesheet'
    if (this.activatedMode !== this.pageName.MangeTimesheet || this.selectedTimeSheetHourStatus === TimesheetStatus.NotSubMitted) {
      return true; // Return true immediately
    }

    // Check if the selected status is not 'ALL'
    if (selectedTimeSheetHourStatus !== TimesheetStatus.ALL) {
      // Check if the position and its time entries exist
      if (position?.position?.time_entries) {
        // Call the checkAnyStatusAssigned function with the time entries and the selected status
        return this.checkAnyStatusAssigned(position.position.time_entries, selectedTimeSheetHourStatus);
      }
      return false; // Return false if position or time entries are missing
    }

    return true; // Return true if the selected status is 'ALL'
  }

  updateEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    this.cacheFilter.setCacheFilters({ ...filter, ...{ filterOpen: this.showFilter } }, page);
  }

  checkEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    if (filter?.filterOpen) {
      this.showFilter = filter.filterOpen;
    }
  }

  ngOnDestroy(): void {
    if (this.activatedMode === this.pageName.MangeTimesheet) {
      this.rememberChanges();
    }
  }

  rememberChangesObjectCreation(): void {
    this.selectedFilterRangeQueryCopy = {
      startDate: this.startDate,
      endDate: this.endDate,
      timesheet: this.selectedTimesheetRange,
      selectedTab: this.currentTab,
      timesheetStatus: this.selectedTimeSheetHourStatus || TimesheetStatus.ALL
    };
    this.selectedFilterRangeQueryCopy?.selectedTab
      ? (this.selectedFilterRangeQueryCopy = {
          ...this.selectedFilterRangeQueryCopy,
          ...this.filterSaveProcess(this.currentTab, true),
          selectedTabValue: this.filterSaveProcess(this.currentTab, true),
          isAnyTabSelected: this.filterSaveProcess(this.currentTab, true) ? true : false,
          weekDays: this.weekDays,
          totalWeeks: this.totalWeeks,
          biWeekRange: this.biWeekRange
        })
      : {};

    if (!this.selectedFilterRangeQueryCopy.isAnyTabSelected) {
      this.isFilterReset = false;
    }
    if (this.activatedMode === this.pageName.MangeTimesheet) {
      this.rememberChanges();
    }
  }

  rememberChanges(): void {
    this.cacheFilter.setCacheFilters(
      { ...this.selectedFilterRangeQueryCopy, rowsPerPage: this.rowsPerPage, currentPage: this.currentPage, order_by: this.activeSort() },
      this.appConstants.MANAGE_SCREENS.MANAGE_TIME
    );
  }

  getRememberChanges(): any {
    return this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.MANAGE_TIME);
  }

  rememberApplyChanges(): void {
    this.dataFilter = this.getRememberChanges();
    if (this.dataFilter) {
      if (this.dataFilter.hasOwnProperty('timesheetStatus')) {
        this.selectedTimeSheetHourStatus = this.dataFilter?.timesheetStatus;
        this.cdr.detectChanges();
      }
      if (this.dataFilter.hasOwnProperty('isAnyTabSelected') && this.dataFilter.isAnyTabSelected) {
        this.currentTab = this.dataFilter.selectedTab;
        this.selectedTimesheetRange = this.dataFilter.timesheet;
        this.filterSaveProcess(this.currentTab, false, { label: this.dataFilter.label, value: this.dataFilter.value });
      } else {
        this.currentTab = this.currentTabName.Client;
        this.activeIndexTab = 0;
      }

      if (this.dataFilter.hasOwnProperty('timesheet')) {
        this.selectedTimesheetRange = this.dataFilter.timesheet;
      }

      if (this.dataFilter?.startDate && this.dataFilter?.endDate) {
        this.startDate = new Date(this.dataFilter?.startDate);
        this.endDate = new Date(this.dataFilter?.endDate);
        this.dataFilter?.biWeekRange ? this.setRemind(this.startDate, this.dataFilter.biWeekRange) : this.setRemind(this.startDate);
      }

      this.selectedFilterRangeQueryCopy = {
        startDate: this.startDate,
        endDate: this.endDate,
        timesheet: this.selectedTimesheetRange,
        selectedTab: this.currentTab
      };
    }
    this.filterApply(false);
  }

  setRemind(date, biWeekRange?) {
    if (TIMESHEET_RANGE.MONTHLY === this.selectedTimesheetRange) {
      this.currentDate?.setMonth(new Date(date)?.getMonth());
    } else if (TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange) {
      this.selectedDate = this.startDate;
    } else if (TIMESHEET_RANGE.BIWEEKLY === this.selectedTimesheetRange) {
      this.selectedDate?.setDate(this.startDate?.getDate());
    }
  }

  resetRemember(): void {
    this.cacheFilter.resetCacheFilters(this.appConstants.MANAGE_SCREENS.MANAGE_TIME);
    this.currentPage = this.appConstants.DEFAULT_PAGE;
    this.rowsPerPage = this.appConstants.DEFAULT_ROWS_PER_PAGE;
  }

  async checkManageTimePermission(): Promise<void> {
    const check = await this.authService.isPermittedAction([this.permissionModules.MANAGE_TIME_ENTRIES]);
    if (check && this.activatedMode === this.pageName.MangeTimesheet) {
      this.isPermissionManageTime = true;
      return;
    }
    this.isPermissionManageTime = false;
  }

  pageChange(event: LazyLoadEvent): void {
    const startDataIndex = event.first;
    this.rowsPerPage = event.rows;
    this.currentPage = Math.ceil(startDataIndex / this.rowsPerPage);
    const storedFilter = this.cacheFilter.getCacheFilters(this.appConstants.MANAGE_SCREENS.MANAGE_TIME);
    this.cacheFilter.setCacheFilters({ ...storedFilter, ...{ rowsPerPage: this.rowsPerPage, currentPage: this.currentPage } }, this.appConstants.MANAGE_SCREENS.MANAGE_TIME);
  }

  isShowPaginator(): boolean {
    return this.activatedMode === this.pageName.MangeTimesheet && this.customers?.length > this.appConstants.DEFAULT_ROWS_PER_PAGE;
  }

  activeSort(event?: Table): string | null {
    if (this.sortFieldName && this.activatedMode === this.pageName.MangeTimesheet) {
      if (this.sortOrderNumber === 1) {
        return `asc:${this.sortFieldName}`;
      } else {
        return `desc:${this.sortFieldName}`;
      }
    }
    return null;
  }

  sortColumn(event: any): void {
    if (this.activatedMode === this.pageName.CreateTimeSheet) {
      return;
    }
    const newSortField = event?.field;
    const newSortOrder = event?.order;

    if (this.sortFieldName === newSortField && this.sortOrderNumber === newSortOrder) {
      return;
    }
    this.sortFieldName = event?.field;
    this.sortOrderNumber = event?.order;
    this.sortColumnFlag = true;
    this.filterApply();
  }

  onMouseHover(event: MouseEvent, day: any, i: number, hours = 0, timeSheets?: any[], hoverComment = false): void {
    event.stopPropagation();
    if (this.isHoverComment) {
      this.isHoverComment = false;
    }

    const hasComment = this.getComment(day, timeSheets, i);
    if (hasComment && !this.isClickedToEditComment && event) {
      this.isHoverComment = true;
      this.onAddCommentClick(event, day, i, hours, timeSheets, hoverComment);
    }
  }

  onMouseLeave(): void {
    this.isHoverComment && this.cancelComment();
  }

  getValidLink(componentsType: ComponentsType, fieldObject: OpenPositionReport, dbTag: string): string {
    const link = this.getValueBYExtendFiled(componentsType, fieldObject, dbTag);
    if (!link) return '';
    return this.appConstants.regexForHyperlink.test(link) ? link : `https://${link}`;
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    if (this.openFilter) {
      if (event.key === 'Escape') {
        event.preventDefault();
        this.closeSideBar(true);
      } else if (event.key === 'Enter') {
        event.preventDefault();
        this.filterApply();
      }
    }
  }

  onEnterDropdown(dropdown: Dropdown): void {
    if (dropdown.overlayVisible) {
      dropdown.overlayVisible = false;
    }
  }
}

// no entery means disable false
//  approved not found then true
//  approved founded  false
