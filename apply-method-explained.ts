// ========================================
// UNDERSTANDING THE .apply() METHOD
// ========================================

// The .apply() method is used to call a function with:
// 1. A specific 'this' context (what 'this' refers to inside the function)
// 2. An array of arguments

// ========================================
// PART 1: BASIC UNDERSTANDING
// ========================================

console.log("=== BASIC .apply() EXAMPLES ===");

// Example 1: Simple function without 'this'
function addNumbers(a: number, b: number): number {
    return a + b;
}

// Normal call
const result1 = addNumbers(5, 3);
console.log("Normal call:", result1); // 8

// Using apply (same result, but different syntax)
const result2 = addNumbers.apply(null, [5, 3]);
console.log("Using apply:", result2); // 8

// Example 2: Function that uses 'this'
function introduce(greeting: string, punctuation: string): string {
    // 'this' refers to whatever object we set it to
    return `${greeting}, my name is ${(this as any).name}${punctuation}`;
}

const person1 = { name: "Alice" };
const person2 = { name: "Bob" };

// Using apply to set different 'this' contexts
const intro1 = introduce.apply(person1, ["Hello", "!"]);
const intro2 = introduce.apply(person2, ["Hi", "."]);

console.log(intro1); // "Hello, my name is Alice!"
console.log(intro2); // "Hi, my name is Bob."

// ========================================
// PART 2: WHY WE NEED .apply() IN DECORATORS
// ========================================

console.log("\n=== WHY DECORATORS NEED .apply() ===");

class Calculator {
    constructor(public calculatorName: string) {}
    
    add(a: number, b: number): number {
        // Notice: this method uses 'this.calculatorName'
        console.log(`${this.calculatorName} is adding ${a} + ${b}`);
        return a + b;
    }
}

// Let's create a decorator that shows the WRONG way and RIGHT way
function LogMethodWrongWay(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args: any[]) {
        console.log("Before calling method");
        
        // WRONG WAY: Call the function directly
        // This loses the 'this' context!
        // const result = originalMethod(args[0], args[1]); 
        // The above would cause 'this.calculatorName' to be undefined
        
        // RIGHT WAY: Use apply to preserve 'this'
        const result = originalMethod.apply(this, args);
        
        console.log("After calling method");
        return result;
    };
}

// Apply the decorator manually
const calcDescriptor = Object.getOwnPropertyDescriptor(Calculator.prototype, 'add');
if (calcDescriptor) {
    LogMethodWrongWay(Calculator.prototype, 'add', calcDescriptor);
}

const myCalc = new Calculator("SuperCalculator");
myCalc.add(10, 20); // Works correctly because we used .apply()

// ========================================
// PART 3: STEP-BY-STEP BREAKDOWN
// ========================================

console.log("\n=== STEP-BY-STEP BREAKDOWN ===");

// Let's trace exactly what happens in a decorator
class ExampleClass {
    constructor(public id: string) {}
    
    processData(data: string): string {
        return `${this.id} processed: ${data}`;
    }
}

// Step 1: Get the original method
const originalProcessData = ExampleClass.prototype.processData;
console.log("Step 1: Got original method");

// Step 2: Create our wrapper function
function wrapperFunction(...args: any[]) {
    console.log("Step 2: Wrapper function called");
    console.log("Step 3: About to call original method");
    
    // Step 3: Call original method with proper context
    // 'this' here refers to the instance of ExampleClass
    const result = originalProcessData.apply(this, args);
    
    console.log("Step 4: Original method finished");
    return result;
}

// Step 4: Replace the original method
ExampleClass.prototype.processData = wrapperFunction;

// Step 5: Test it
const example = new ExampleClass("EX001");
const finalResult = example.processData("test data");
console.log("Final result:", finalResult);

// ========================================
// PART 4: WHAT HAPPENS WITHOUT .apply()
// ========================================

console.log("\n=== WHAT HAPPENS WITHOUT .apply() ===");

class BrokenExample {
    constructor(public name: string) {}
    
    sayHello(): string {
        return `Hello from ${this.name}`;
    }
}

// Decorator that DOESN'T use apply (broken)
function BrokenDecorator(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args: any[]) {
        console.log("Calling method without proper 'this'");
        
        // WRONG: Call without apply - 'this' context is lost
        try {
            const result = originalMethod(); // No 'this' context!
            return result;
        } catch (error) {
            console.log("Error:", error.message);
            return "Method failed due to missing 'this' context";
        }
    };
}

// Apply broken decorator
const brokenDescriptor = Object.getOwnPropertyDescriptor(BrokenExample.prototype, 'sayHello');
if (brokenDescriptor) {
    BrokenDecorator(BrokenExample.prototype, 'sayHello', brokenDescriptor);
}

const brokenInstance = new BrokenExample("TestName");
console.log(brokenInstance.sayHello()); // This will fail or return undefined

// ========================================
// PART 5: COMPARISON OF CALL METHODS
// ========================================

console.log("\n=== COMPARISON: .apply() vs .call() vs direct call ===");

function testFunction(x: number, y: number): string {
    return `Result: ${x + y}, Context: ${(this as any)?.context || 'none'}`;
}

const contextObj = { context: "test-context" };

// Method 1: Direct call (no custom 'this')
const direct = testFunction(1, 2);
console.log("Direct call:", direct);

// Method 2: Using .call() (arguments passed individually)
const withCall = testFunction.call(contextObj, 1, 2);
console.log("Using .call():", withCall);

// Method 3: Using .apply() (arguments passed as array)
const withApply = testFunction.apply(contextObj, [1, 2]);
console.log("Using .apply():", withApply);

// ========================================
// SUMMARY
// ========================================

console.log("\n=== SUMMARY ===");
console.log("1. .apply() calls a function with a specific 'this' context");
console.log("2. .apply() takes arguments as an array: func.apply(thisContext, [arg1, arg2])");
console.log("3. In decorators, we use .apply() to preserve the original 'this' context");
console.log("4. Without .apply(), the decorated method would lose access to 'this.property'");
console.log("5. .call() is similar but takes individual arguments: func.call(thisContext, arg1, arg2)");
