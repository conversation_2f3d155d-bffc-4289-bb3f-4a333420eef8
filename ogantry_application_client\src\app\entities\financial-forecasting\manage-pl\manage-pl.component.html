<span class="p-l-ReportWrapper">
  <mat-drawer-container class="h-100" id="benchReportContainer" (backdropClick)="onCloseSideBar(false)">
    <!-- <mat-sidenav class="detail-sidebar" #sidebarFilter mode="over" position="end" disableClose> -->
    <mat-drawer class="manage-pl-detail-sidebar" #sidebarFilter mode="over" position="end" disableClose [opened]="openFilter">
      <app-apply-filter
        [(filterData)]="dataFilter"
        (closeSidebarEvent)="onCloseSideBar($event)"
        [clientGroups]="clientGroups"
        [projectGroups]="projectGroups"
        [project]="projects"
        [client]="client"
        [sidebarIcons]="sidebarButtons"
        (getProjectsIds)="getProjectsIds()"
        (getClientsIds)="getClientsIds()"
        [statuses]="statuses"
        [tags]="groupedCategory"
        [savedSelectedTags]="selectedTags"
        [defaultSelectedStatuses]="defaultStatuses"
      ></app-apply-filter>
    </mat-drawer>
    <mat-drawer-content class="detail-sidebar-content">
      <ng-container *ngTemplateOutlet="managePL"></ng-container>
    </mat-drawer-content>
  </mat-drawer-container>
  <ng-template #managePL>
    <div class="card card-custom gutter-b" id="managePL">
      <app-card-header
        [cardTitle]="cardTitle"
        [cardSubTitle]="cardSubTitle"
        [buttons]="buttons"
        [exportButton]="exportButtons"
        [showSplitButton]="true"
        [splitButtonDropDownOption]="splitButtonDropDownOption"
      ></app-card-header>
      <div class="card-body">
        <div class="row">
          <div class="col-12">
            <app-selected-filter-tags
              [tags]="tags"
              (filterReset)="resetFilter()"
              (saveFilter)="onSaveFilter()"
              (onCancel)="onCancelFilter($event)"
              (onRemoveStatus)="onRemoveStatusFilter($event)"
            ></app-selected-filter-tags>
          </div>
        </div>
        <div *ngIf="!resizeFlag; else resize_table">
          <ng-container *ngIf="!finalProjectionData?.length && !(loading$ | async)">
            <p class="filter-note ml-5">Please apply filter to load the P&L Comparision.</p>
          </ng-container>
          <p-treeTable
            [value]="finalProjectionData"
            *isFetchingData="loading$"
            frozenWidth="390px"
            [frozenColumns]="frozenCols"
            [scrollable]="true"
            [columns]="tableHeaders"
            (onNodeExpand)="makeRowsSameHeight()"
            (onNodeCollapse)="makeRowsSameHeight()"
          >
            <ng-template pTemplate="colgroup" let-columns>
              <colgroup>
                <col *ngFor="let col of columns" style="width: 120px" />
                <col style="width: 120px" />
              </colgroup>
            </ng-template>
            <ng-template pTemplate="header" let-columns>
              <tr>
                <th *ngFor="let col of columns">
                  {{ col.monthLabel }}
                </th>
                <th class="p-r-15">Total</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
              <tr style="height: 57px">
                <ng-container *ngFor="let monthlyData of getMonthlyValues(rowData, rowNode) | keyvalue : preserveOriginalOrder">
                  <ng-container *ngIf="isTypeWithDirectDisplay(rowNode); else monthlyValues">
                    <ng-container *ngIf="rowNode?.node?.data?.type !== 'SG&A'; else sgaPermissionBlock">
                      <td *hasAnyPermission="permissionModules.MANAGE_PL_PLUGS; disableEvent: true" class="text-underline pointer" (click)="showDialog(rowData, monthlyData)">
                        {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                      </td>
                    </ng-container>

                    <ng-template #sgaPermissionBlock>
                      <ng-container *hasAnyPermission="permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                        <td *hasAnyPermission="permissionModules.MANAGE_PL_PLUGS; disableEvent: true" class="text-underline pointer" (click)="showDialog(rowData, monthlyData)">
                          {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                        </td>
                      </ng-container>
                    </ng-template>
                  </ng-container>
                  <ng-template #monthlyValues>
                    <ng-container *ngIf="rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit'; else monthlyValueExceptNetMargin">
                      <td *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                        <ng-template
                          [ngTemplateOutlet]="colValueExceptNetMargin"
                          [ngTemplateOutletContext]="{ rowNode: rowNode, rowData: rowData, monthlyData: monthlyData }"
                        ></ng-template>
                      </td>
                    </ng-container>
                    <ng-template #monthlyValueExceptNetMargin>
                      <td>
                        <ng-template
                          [ngTemplateOutlet]="colValueExceptNetMargin"
                          [ngTemplateOutletContext]="{ rowNode: rowNode, rowData: rowData, monthlyData: monthlyData }"
                        ></ng-template>
                      </td>
                    </ng-template>
                  </ng-template>
                </ng-container>
                <ng-container class="font-weight-bold p-r-15">
                  <ng-container
                    *ngIf="
                      rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit' || rowNode?.node?.data?.type === 'SG&A';
                      else monthlyValueExceptNetMargin
                    "
                  >
                    <td *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                      {{
                        rowNode?.level === 0
                          ? rowNode?.node?.data?.type !== 'Net Margin'
                            ? '$' + (getTotal(rowData, rowNode) | addCommasToNumbers)
                            : (getTotal(rowData, rowNode) | addCommasToNumbers) + '%'
                          : '$' + (getTotal(rowData, rowNode) | addCommasToNumbers)
                      }}
                    </td>
                  </ng-container>
                  <ng-template #monthlyValueExceptNetMargin>
                    <td>
                      {{
                        rowNode?.level === 0
                          ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                            ? '$' + (getTotal(rowData, rowNode) | addCommasToNumbers)
                            : (getTotal(rowData, rowNode) | addCommasToNumbers) + '%'
                          : rowNode.parent.data.type !== 'Gross Margin'
                            ? '$' + (getTotal(rowData, rowNode) | addCommasToNumbers)
                            : (getTotal(rowData, rowNode) | addCommasToNumbers)
                      }}{{ rowNode?.level === 1 && rowNode.parent.data.type === 'Gross Margin' ? '%' : '' }}
                    </td>
                  </ng-template>
                </ng-container>
              </tr>
            </ng-template>
            <ng-template pTemplate="frozenbody" let-rowNode let-rowData="rowData">
              <tr style="height: 57px">
                <ng-container>
                  <ng-container
                    *ngIf="
                      rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit' || rowNode?.node?.data?.type === 'SG&A';
                      else nameAcceptingNetMargin
                    "
                  >
                    <td [ngClass]="getStyle(rowNode)" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                      <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                      <span [title]="rowData?.type"> {{ rowData?.type }} </span>
                    </td>
                    <td [title]="rowData?.name" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                      {{ rowData?.name }}
                    </td>
                  </ng-container>
                  <ng-template #nameAcceptingNetMargin>
                    <td [ngClass]="getStyle(rowNode)">
                      <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                      <span [title]="rowData?.type">{{ rowData?.type }}</span>
                    </td>
                    <td [title]="rowData?.name">
                      {{ rowData?.name }}
                    </td>
                  </ng-template>
                </ng-container>
              </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
              <tr *ngIf="!loading$">
                <td colspan="4" class="center-align">No Data found.</td>
              </tr>
            </ng-template>
          </p-treeTable>
          <p-dialog
            dismissableMask="true"
            backdrop="false"
            position="top-right"
            [(visible)]="showExportOptionDialog"
            [modal]="true"
            class="export-dialog"
            [draggable]="false"
            [resizable]="false"
          >
            <ng-template pTemplate>
              <div class="export-action-listing" *ngIf="showExportOptions">
                <button pButton class="btn p-button-text mb-2 mt-2" icon="pi pi-file-o" iconPos="left" (click)="exportReport('csv')">Export CSV</button>
                <button pButton class="btn p-button-text mb-2" icon="pi pi-file-pdf" iconPos="left" (click)="exportReport('pdf')">Export PDF</button>
                <button pButton class="btn p-button-text mb-2" icon="pi pi-file-excel" iconPos="left" (click)="exportReport('excel')">Export Excel</button>
              </div>
            </ng-template>
          </p-dialog>
        </div>
        <ng-template #resize_table>
          <div class="scrollable-content">
            <ng-container *ngIf="!finalProjectionData?.length && !(loading$ | async)">
              <p class="filter-note ml-5">Please apply filter to load the P&L Comparision.</p>
            </ng-container>
            <p-treeTable
              [value]="finalProjectionData"
              *isFetchingData="loading$"
              [scrollable]="true"
              [scrollHeight]="height"
              [columns]="tableHeaders"
              (onNodeExpand)="makeRowsSameHeight()"
              (onNodeCollapse)="makeRowsSameHeight()"
            >
              <ng-template pTemplate="colgroup" let-columns>
                <colgroup>
                  <col style="width: 100px" />
                  <col style="width: 100px" />
                  <col *ngFor="let col of columns" style="width: 120px" />
                  <col style="width: 120px" />
                </colgroup>
              </ng-template>
              <ng-template pTemplate="header" let-columns>
                <tr>
                  <th></th>
                  <th></th>
                  <th *ngFor="let col of columns">
                    {{ col.monthLabel }}
                  </th>
                  <th>Total</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
                <tr style="height: 57px">
                  <td [ngClass]="getStyle(rowNode)">
                    <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                    <span [title]="rowData?.type">{{ rowData?.type }}</span>
                  </td>
                  <td [title]="rowData?.name">
                    {{ rowData?.name }}
                  </td>
                  <td
                    *ngFor="let monthlyData of getMonthlyValues(rowData, rowNode) | keyvalue : preserveOriginalOrder"
                    [ngClass]="{
                      pointer: rowNode.level === 2 && rowNode.parent.data.type === 'Bench'
                    }"
                  >
                    <span
                      *ngIf="
                        rowNode?.node?.data?.type === 'SG&A' || rowNode?.node?.data?.type === 'Revenue Adjustment' || rowNode?.node?.data?.type === 'COGS Adjustment';
                        else monthlyValues
                      "
                      class="text-underline pointer"
                      (click)="showDialog(rowData, monthlyData)"
                    >
                      {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                    </span>
                    <ng-template #monthlyValues>
                      <span
                        [ngClass]="{
                          pointer: rowNode.level === 2 && rowNode.parent.data.type === 'Bench'
                        }"
                        [title]="rowNode.level === 2 && rowNode.parent.data.type === 'Bench' ? 'Click me for position details' : ''"
                        (click)="
                          rowNode.level === 2 && rowNode.parent.data.type === 'Bench'
                            ? monthlyValuesClick(rowData, monthlyData, popOver, getIsBenchNegative(rowNode?.parent?.data?.type, monthlyData?.value))
                            : ''
                        "
                        container="body"
                        [ngbPopover]="popOverContent"
                        popoverClass="custom-pop"
                        #popOver="ngbPopover"
                        triggers="manual"
                      >
                        {{
                          rowNode?.level === 0
                            ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                              ? '$' + (monthlyData?.value | addCommasToNumbers)
                              : (monthlyData?.value | addCommasToNumbers) + '%'
                            : rowNode?.parent?.data?.type !== 'Gross Margin'
                              ? '$' + (monthlyData?.value | addCommasToNumbers)
                              : (monthlyData?.value | addCommasToNumbers)
                        }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                      </span>
                      <span
                        class="info-icon"
                        *ngIf="getIsBenchNegative(rowNode?.parent?.data?.type, monthlyData?.value)"
                        [ngbTooltip]="showBenchToolTip()"
                        tooltipClass="negative-bench-tooltip"
                        placement="auto"
                        appendTo="body"
                      >
                        <fa-icon [icon]="'exclamation-triangle'"></fa-icon>
                      </span>
                      <ng-template #popOverContent>
                        <div class="d-block text-right">
                          <button class="btn-close-icon" (click)="closePopOver(popOver)">
                            <fa-icon icon="times"></fa-icon>
                          </button>
                        </div>
                        <p-table responsiveLayout="scroll" #dt [value]="activeEmployee" dataKey="id" [loading]="loadingEmp" styleClass="p-datatable-customers">
                          <ng-template pTemplate="header">
                            <tr>
                              <th id="company" class="header-width">Client</th>
                              <th id="contactPerson" class="contact-person-header-width">Project</th>
                              <th id="phone" class="header-width">Start Date</th>
                              <th id="email" class="header-width">End Date</th>
                              <th id="email" class="header-width">Project Status</th>
                            </tr>
                          </ng-template>
                          <ng-template pTemplate="body" let-customer>
                            <tr>
                              <td>
                                {{ customer?.customer?.project?.customer?.name }}
                              </td>
                              <td>
                                <a
                                  class="ellipses"
                                  *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true"
                                  [routerLink]="[appRoutes.EDIT_PROJECT, customer.project.id]"
                                >
                                  <span [title]="customer.project.name">
                                    {{ customer.project.name }}
                                  </span>
                                </a>
                              </td>
                              <td>
                                {{ customer.start_date | date : 'MM/dd/yyyy' }}
                              </td>
                              <td>
                                {{ customer.end_date | date : 'MM/dd/yyyy' }}
                              </td>
                              <td>
                                <span [title]="customer?.customer?.project?.status">
                                  {{ customer?.customer?.project?.status }}
                                </span>
                              </td>
                            </tr>
                          </ng-template>
                          <ng-template pTemplate="emptymessage">
                            <tr>
                              <td colspan="6" class="text-center">No Data found.</td>
                            </tr>
                          </ng-template>
                        </p-table>
                      </ng-template>
                    </ng-template>
                  </td>
                  <td class="font-weight-bold text-right">
                    {{
                      rowNode?.level === 0
                        ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                          ? '$' + (getTotal(rowData, rowNode) | addCommasToNumbers)
                          : (getTotal(rowData, rowNode) | addCommasToNumbers) + '%'
                        : rowNode.parent.data.type !== 'Gross Margin'
                          ? '$' + (getTotal(rowData, rowNode) | addCommasToNumbers)
                          : (getTotal(rowData, rowNode) | addCommasToNumbers)
                    }}{{ rowNode?.level === 1 && rowNode.parent.data.type === 'Gross Margin' ? '%' : '' }}
                  </td>
                </tr>
              </ng-template>
              <ng-template pTemplate="emptymessage">
                <tr *ngIf="!loading$">
                  <td colspan="4" class="center-align">No Data found.</td>
                </tr>
              </ng-template>
            </p-treeTable>
          </div>
        </ng-template>
      </div>
    </div>
  </ng-template>
  <p-dialog
    dismissableMask="true"
    backdrop="false"
    position="top-right"
    [(visible)]="showFilterListDialog"
    [modal]="true"
    class="filter-dialog"
    [draggable]="false"
    [resizable]="false"
  >
    <ng-template pTemplate>
      <div class="filter-listing" *ngIf="showSavedFilter">
        <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
        <div *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true" class="title">Shared Filters</div>
        <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
          <span *ngIf="sharedFilters?.length; else noData">
            <div
              class="form-check filter-body"
              *ngFor="let filterOption of sharedFilters"
              [ngClass]="{
                'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
              }"
            >
              <label class="form-check-label">
                <input
                  [formControl]="selectedFilterFormControl"
                  (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                  type="radio"
                  class="form-check-input custom-radio"
                  [value]="filterOption"
                  name="filteroption"
                />{{ filterOption?.query_filter?.name }}
              </label>
              <div class="filter-icons">
                <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                  <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                    <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                  </a>
                  <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                    <em class="fa-solid fa-share" title="UnShare Filter"></em>
                  </a>
                  <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                    <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                  </a>
                  <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                    <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)">
                    </span>
                  </a>
                </ng-container>
              </div>
            </div>
          </span>
        </ng-container>

        <ng-template #noData>
          <div>-</div>
        </ng-template>
        <div class="title">My Filters</div>
        <span *ngIf="myFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of myFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons">
              <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
              </a>
              <a
                class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
                (click)="shareFilter(filterOption)"
                *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
              >
                <em class="fa-solid fa-share" title="Share Filter"></em>
              </a>
              <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
              </a>
              <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
              </a>
            </div>
          </div>
        </span>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
    <div class="form-group first" *ngIf="editFilterObj">
      <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
      <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
    </div>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center">
        <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
        <button type="button" class="btn-save" (click)="applySelectedFilterAndUpdateUrl()" [disabled]="!(selectedFilter?.length > 0)" [isSubmitting]="isSubmitting">Apply</button>
        <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog header="Delete" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
    <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center">
        <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
        <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
    <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center">
        <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
        <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog
    [header]="adjustValueObj?.title"
    [(visible)]="showAdjustValuesDialog"
    [modal]="true"
    class="confirm-dialog"
    [baseZIndex]="10000"
    [draggable]="false"
    [resizable]="false"
  >
    <form *ngIf="adjustValueObj" class="p-m-0 mt-2 mb-2" autocomplete="off" novalidate="novalidate" id="add_expense_form">
      <div *isFetchingData="loading$$">
        <h5 class="form-group first">{{ adjustValueObj?.displayMonth }}</h5>
        <div class="form-group first">
          <div class="p-inputgroup">
            <span class="p-inputgroup-addon">$</span>
            <p-inputNumber inputId="integeronly" [(ngModel)]="adjustValueObj.amount" name="amt" (onInput)="valueChange('amount')"></p-inputNumber>
          </div>
          <small *ngIf="showAmtError" class="form-text text-danger"> Amount is required</small>
        </div>
        <div class="form-group first">
          <h5>Note</h5>
          <textarea rows="5" cols="30" pInputTextarea [(ngModel)]="adjustValueObj.reason" name="reason" required (input)="valueChange('reason')"></textarea>
          <small *ngIf="showNoteError" class="form-text text-danger"> Note is required</small>
        </div>
      </div>
    </form>

    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center" *isFetchingData="loading$$">
        <button type="button" class="btn-cancel" (click)="closeAdjustModal()">Cancel</button>
        <button type="button" class="btn-save" (click)="saveAdjustValue()" [isSubmitting]="isSubmitting">Save</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog
    id="sga-dialog"
    [header]="'Adjust' + ' ' + openPlugName"
    [(visible)]="showMultipleSgaDialog"
    [modal]="true"
    class="retainer-plug-dialog lock-dialog"
    [baseZIndex]="10000"
    [draggable]="false"
    [resizable]="false"
    [style]="{ width: '30vw' }"
  >
    <div *ngIf="sgaPlugs && sgaPlugs.length > 0" class="p-m-0 mt-1 mb-1">
      <div *isFetchingData="sgaPlugsLoading$">
        <h6 class="form-group first text text-wrap mb-2">Adjusting the {{ openPlugName }} allows you to update multiple months at once with the same amount and reason.</h6>
        <div class="form-group mt-2">
          <h5>Select Months to Update</h5>
          <p-multiSelect
            [options]="sgaMonthOptions"
            [(ngModel)]="selectedSgaMonths"
            defaultLabel="Select months"
            optionLabel="label"
            [filter]="true"
            filterBy="label"
            display="chip"
            (onChange)="onSgaMonthSelectionChange()"
            [maxSelectedLabels]="3"
            [selectedItemsLabel]="appConstants.selectedMonthsLabel"
            styleClass="full-width"
            appendTo="body"
          >
            <ng-template let-item pTemplate="item">
              <div class="month-option-item">
                <span>{{ item.label }}</span>
                <span class="month-option-amount">${{ item.data.amount | number : '1.2-2' }}</span>
              </div>
            </ng-template>
          </p-multiSelect>
        </div>

        <div class="form-group">
          <h5>Amount</h5>
          <div class="p-inputgroup">
            <span class="p-inputgroup-addon">$</span>
            <p-inputNumber
              inputId="sgaAmount"
              [(ngModel)]="multipleSgaAmount"
              name="sgaAmount"
              (onInput)="multipleSgaValueChange('amount')"
              [style]="{ width: '100%' }"
            ></p-inputNumber>
          </div>
          <small *ngIf="showMultipleSgaAmountError" class="form-text text-danger">Amount is required</small>
        </div>

        <div class="form-group">
          <h5>Note</h5>
          <textarea
            rows="4"
            cols="30"
            pInputTextarea
            [(ngModel)]="multipleSgaReason"
            name="sgaReason"
            (input)="multipleSgaValueChange('reason')"
            [placeholder]="'Enter a reason for updating the' + ' ' + openPlugName"
            [style]="{ width: '100%' }"
          ></textarea>
          <small *ngIf="showMultipleSgaError" class="form-text text-danger">Note is required</small>
        </div>
      </div>
    </div>
    <div *ngIf="!sgaPlugs || sgaPlugs.length === 0" class="p-m-0 mt-2 mb-2">
      <div class="alert alert-info">No months available for {{ openPlugName }} adjustment.</div>
    </div>

    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center" *isFetchingData="sgaPlugsLoading$">
        <button type="button" class="btn-cancel" (click)="closeMultipleSgaDialog()">Cancel</button>
        <button
          [disabled]="!this.selectedSgaMonths?.length"
          type="button"
          class="btn-save"
          (click)="saveMultipleSgaPlugs()"
          [isSubmitting]="isMultipleSgaSubmitting"
          *ngIf="sgaPlugs && sgaPlugs.length > 0"
        >
          Adjust {{ openPlugName }}
        </button>
      </div>
    </ng-template>
  </p-dialog>
</span>

<p-dialog
  header=""
  [(visible)]="showPausedProjectDialog"
  [modal]="true"
  class="confirm-dialog-paushed"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [closable]="false"
  [style]="{ width: '725px' }"
>
  <ng-container *ngIf="!isResumeValidationInProgress; else calculating">
    <div class="ml-2">
      <div class="d-flex align-items-center">
        <h5 class="p-m-0 mb-3 text-wrap">The following project’s financial calculations are paused while edits are in progress</h5>
      </div>
      <p-table
        class="paused-project-table"
        [resizableColumns]="true"
        [lazy]="true"
        [style]="{ overflow: 'auto!important' }"
        #dt
        [value]="pausedProjectList"
        responsiveLayout="scroll"
      >
        <ng-template pTemplate="header">
          <tr class="sticky-row-1">
            <th>Project</th>
            <th>Client</th>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Status</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-columns="columns">
          <tr>
            <td>{{ rowData?.project?.name }}</td>
            <td>{{ rowData?.project?.customer?.name }}</td>
            <td>{{ rowData?.project?.start_date | date : 'MM/dd/yyyy' }}</td>
            <td>{{ rowData?.project?.end_date | date : 'MM/dd/yyyy' }}</td>
            <td>{{ rowData?.project?.status }}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-nowrap justify-content-end align-items-center">
        <button type="button" class="btn-cancel" (click)="wait()" [disabled]="isResumeValidationInProgress">Recalculate Now</button>
        <button type="button" class="btn-save" (click)="continue()" [disabled]="isResumeValidationInProgress">Proceed using previous financial calculations</button>
      </div>
    </ng-template>
  </ng-container>
  <ng-template #calculating>
    <div class="ml-2">
      <div class="project-name">
        <h5 class="p-m-0 mb-3 text-wrap">
          Calculating cost for <span class="paused-project-name">{{ calculatingProjectName }}</span> Project...
        </h5>
      </div>
      <span [ngClass]="{ 'recalculating-message': isResumeValidationInProgress }" *ngIf="isResumeValidationInProgress">
        <i class="material-icons rotating-icon mr-2">hourglass_empty</i>
        <ng-container>{{ rotatingMessage }}...</ng-container>
      </span>
    </div>
  </ng-template>
</p-dialog>

<ng-template #colValueExceptNetMargin let-rowNode="rowNode" let-rowData="rowData" let-monthlyData="monthlyData">
  <span
    [ngClass]="{
      pointer: rowNode.level === 2 && rowNode.parent.data.type === 'Bench'
    }"
    [title]="rowNode.level === 2 && rowNode.parent.data.type === 'Bench' ? 'Click me for position details' : ''"
    (click)="
      rowNode.level === 2 && rowNode.parent.data.type === 'Bench'
        ? monthlyValuesClick(rowData, monthlyData, popOver, getIsBenchNegative(rowNode?.parent?.data?.type, monthlyData?.value))
        : ''
    "
    container="body"
    [ngbPopover]="popOverContent"
    popoverClass="custom-pop"
    #popOver="ngbPopover"
    triggers="manual"
  >
    <ng-container *ngIf="rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit'; else monthlyValueExceptNetMargin">
      <span *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
        {{
          rowNode?.level === 0
            ? rowNode?.node?.data?.type !== 'Net Margin'
              ? '$' + (monthlyData?.value | addCommasToNumbers)
              : (monthlyData?.value | addCommasToNumbers) + '%'
            : '$' + (monthlyData?.value | addCommasToNumbers)
        }}
      </span>
    </ng-container>
    <ng-template #monthlyValueExceptNetMargin>
      <span *ngIf="rowNode?.parent?.parent?.data?.type === 'Projects'; else monthlyValueExceptPosition">
        <span class="" *ngIf="rowNode?.parent?.parent?.data?.type === 'Projects'" [ngbTooltip]="tipContent" tooltipClass="negative-bench-tooltip" placement="auto" appendTo="body">
          {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
        </span>
        <ng-template #tipContent>
          <div *ngIf="getSeparateExpense(rowData, monthlyData)" [innerHTML]="getSeparateExpense(rowData, monthlyData)"></div>
        </ng-template>
      </span>
      <ng-template #monthlyValueExceptPosition>
        {{
          rowNode?.level === 0
            ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
              ? '$' + (monthlyData?.value | addCommasToNumbers)
              : (monthlyData?.value | addCommasToNumbers) + '%'
            : rowNode?.parent?.data?.type !== 'Gross Margin'
              ? '$' + (monthlyData?.value | addCommasToNumbers)
              : (monthlyData?.value | addCommasToNumbers)
        }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
      </ng-template>
    </ng-template>
  </span>
  <span
    class="info-icon"
    *ngIf="getIsBenchNegative(rowNode?.parent?.data?.type, monthlyData?.value)"
    [ngbTooltip]="showBenchToolTip()"
    tooltipClass="negative-bench-tooltip"
    placement="auto"
    appendTo="body"
  >
    <fa-icon [icon]="'exclamation-triangle'"></fa-icon>
  </span>
  <ng-template #popOverContent>
    <div class="d-block text-right">
      <button class="btn-close-icon" (click)="closePopOver(popOver)">
        <fa-icon icon="times"></fa-icon>
      </button>
    </div>
    <p-table responsiveLayout="scroll" #dt [value]="activeEmployee" dataKey="id" [loading]="loadingEmp" styleClass="p-datatable-customers">
      <ng-template pTemplate="header">
        <tr>
          <th id="company" class="header-width">Client</th>
          <th id="contactPerson" class="contact-person-header-width">Project</th>
          <th id="phone" class="header-width">Start Date</th>
          <th id="email" class="header-width">End Date</th>
          <th id="email" class="header-width">Project Status</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-customer let-i="rowIndex">
        <tr class="note-row" *ngIf="customer.isNegative && i == 0">
          <td colspan="5">{{ customer?.length }} Note: Bench is negative because this person's billable hours exceed their expected utilization for this month.</td>
        </tr>
        <tr>
          <td>
            {{ customer?.customer?.project?.customer?.name }}
          </td>
          <td>
            <a class="ellipses" *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true" [routerLink]="[appRoutes.EDIT_PROJECT, customer.project.id]">
              <span [title]="customer.project.name">
                {{ customer.project.name }}
              </span>
            </a>
          </td>
          <td>
            {{ customer.start_date | date : 'MM/dd/yyyy' }}
          </td>
          <td>
            {{ customer.end_date | date : 'MM/dd/yyyy' }}
          </td>
          <td>
            <span [title]="customer?.customer?.project?.status">
              {{ customer?.customer?.project?.status }}
            </span>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="text-center">No Data found.</td>
        </tr>
      </ng-template>
    </p-table>
  </ng-template>
</ng-template>
