:host {
  .aside {
    height: 100%;

    .aside-menu {
      margin: 0;
    }

    .menu-icon {
      margin-left: 25px;
    }
  }

  // fixed text line break issue on minimized aside hover
  .menu-text {
    white-space: nowrap;
  }
}
@media (min-width: 992px) {
  kt-brand {
    margin: 30px 0;
  }
}

.aside-menu .menu-nav > .menu-item > .menu-link .menu-icon {
  color: white;
}

.aside-menu .menu-nav > .menu-item.menu-item-open > .menu-link .menu-arrow {
  padding-left: 15px;
}

.aside-menu .menu-nav > .menu-item > .menu-link .menu-arrow {
  color: white;
  padding-right: 15px;
}

.aside-menu .menu-nav > .menu-item > .menu-submenu .menu-subnav > .menu-item > .menu-link {
  padding: 0 25px 0 51px;
}
.svg-icon {
  width: 20px;
  margin-left: 15px;
  margin-right: 8px;
}

.manage-people {
  width: 23px !important;
  margin-right: 5px !important;
}

@-moz-document url-prefix() {
  .menu-link > .svg-icon {
    margin-left: 4px !important;
    margin-right: 2px !important;
    padding: 10px;
  }
}

.aside-menu .menu-nav > .menu-item > .menu-link .menu-arrow:before {
  transform: rotate(90deg);
  font-family: '<PERSON>';
}

.aside-menu .menu-nav > .menu-item.menu-item-here-1 > .menu-link .menu-text {
  color: #d3a75d;
}

.aside-menu .menu-nav > .menu-item.menu-item-open > .menu-link .menu-arrow {
  transform: rotate(180deg);
  font-family: 'Ki';
}
.aside-menu .menu-nav > .menu-item.menu-item-here-1 > .menu-link .menu-arrow {
  color: #d3a75d;
}

.aside-menu .menu-nav > .menu-item > .menu-link .menu-text {
  color: #ffffff;
}

.svg-icon.svg-icon-sm {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  margin-left: 18px;
}

.profile {
  display: flex;
  align-items: center;
  height: 70px;
  background-color: #45386d;
}

.filter-list {
  color: #9899ac;
  list-style: none;
  font-size: larger;
  font-weight: 500;
  padding: 10px;
  cursor: pointer;
}

.filter-list:hover {
  color: #ffffff;
}

.listOfFilter {
  margin: 10px;
  padding-inline-start: 0px;
}

.menu-arrow-right:before {
  font-family: 'ki' !important;
}

::ng-deep .filter {
  &.p-overlaypanel-flipped:before,
  &.p-overlaypanel-flipped:after {
    visibility: hidden;
  }
  &.p-overlaypanel {
    background: #4b3f72;
    left: 0px !important;
    margin-left: 265px !important;
    margin-top: -45px !important;
  }
  &.p-overlaypanel-content {
    width: 200px !important;
    padding: 0 !important;
  }
}

// Visual indicator for menu items with sublinks
.menu-item.menu-item-submenu > .menu-link {
  position: relative;

  // Add a subtle background highlight
  background: rgba(211, 167, 93, 0.1) !important;

  // Add a left border indicator
  border-left: 3px solid #d3a75d !important;

  // Add an always-visible arrow indicator
  &::after {
    content: '▶' !important;
    position: absolute !important;
    right: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: #d3a75d !important;
    font-size: 12px !important;
    font-weight: bold !important;
    opacity: 0.8 !important;
    z-index: 10 !important;
    transition: all 0.3s ease !important;
  }

  // Enhance the indicator on hover
  &:hover::after {
    opacity: 1 !important;
    transform: translateY(-50%) scale(1.1) !important;
    color: #ffffff !important;
  }
}

// Ensure the existing menu arrow doesn't conflict
.menu-item.menu-item-submenu > .menu-link .menu-arrow {
  display: none !important;
}
