@import '/src/assets/sass/components/variables.bootstrap';
.benchReportWrapper {
  #benchReportContainer {
    .detail-sidebar {
      width: 500px;
      max-width: 100%;
    }

    .p-calendar .p-inputtext {
      height: 60px !important;
    }

    .add-contact .card-body.create-card {
      box-shadow: none;
      margin: 0;
      padding: 20px !important;
    }

    .form-check {
      bottom: 4px;
      position: absolute;
    }

    .form-group {
      padding-bottom: 0;
      &:first-child {
        padding-top: 0;
      }
    }

    ::ng-deep .dropdown .p-dropdown,
    ::ng-deep .p-multiselect {
      width: 100%;
      height: 100%;
      border-radius: 9px !important;
      border: none !important;
      background-color: #f8f8ff !important;
      min-height: 60px !important;
      padding-top: 1rem;
    }

    ::ng-deep .dropdown .p-dropdown .p-dropdown-label,
    ::ng-deep .p-multiselect .p-multiselect-label {
      color: #000000;
      font-family: Poppins;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: -0.32px;
      line-height: 25px;
      padding-top: 0.5rem;
      padding-left: 1rem;
    }

    ::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder,
    ::ng-deep .p-multiselect .p-multiselect-label.p-placeholder {
      color: #b5b5c3 !important;
    }

    ::ng-deep .p-multiselect:not(.p-disabled).p-focus {
      box-shadow: none;
    }

    ::ng-deep .range-calender .p-calendar-w-btn {
      width: 100%;
    }

    ::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
      border: 0;
      background-color: #f8f8ff !important;
      height: 60px;
    }

    ::ng-deep .p-button {
      background: #4b3f72;
      border-color: #4b3f72;
    }

    ::ng-deep .p-button:enabled:hover {
      background: #574985;
      border-color: #574985;
    }

    ::ng-deep .p-button.p-button-icon-only {
      width: 3.357rem;
    }

    ::ng-deep .p-inputtext:enabled:focus {
      box-shadow: none;
    }

    ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
      border-radius: 50%;
    }

    ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
    ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
    ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
    ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
      background: #e9ecef;
      border-color: transparent;
      color: #495057;
      border-radius: 50%;
    }

    ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight),
    ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight),
    ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight),
    ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight) {
      border-radius: 50%;
      background-color: #f4f5f8;
    }

    ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page {
      font-size: 14px;
      &.p-highlight {
        background: #4b3f72;
        border-color: #e3f2fd;
        color: #ffffff;
        border-radius: 50%;
      }
    }

    ::ng-deep .table .p-datatable .p-datatable-thead > tr > th {
      height: 36px;
      color: #4b3f72;
      font-family: Poppins;
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 21px;
      border-bottom: none;
      border: 1px solid #edeff3;
      background-color: #ecedf6 !important;
      position: sticky;
      top: 0px;
      padding: 0.5rem 1rem;
    }
    .header-width {
      width: 10%;
    }
    .header-width-action {
      width: 3% !important;
      max-width: 3% !important;
      display: flex !important;
      flex-direction: column;
    }
    .text-number-right {
      text-align: right !important;
    }
    input[type='radio'] {
      appearance: none;
    }

    input[type='radio']:checked:before {
      background: #4b3f72;
      color: #ffffff;
      content: '\2713';
      text-align: center;
      appearance: auto;
    }
    .form-check {
      position: relative !important;
    }
    .form-check-label {
      margin-bottom: 0.5rem !important;
    }

    input[type='radio']:before {
      border: 1px solid #4b3f72;
      border-radius: 1rem;
      content: '\00a0';
      display: inline-block;
      font: 16px/1em sans-serif;
      height: 16px;
      margin: 0 0.25em 0 0;
      padding: 0;
      vertical-align: top;
      width: 16px;
      appearance: auto;
    }

    ::ng-deep .table .p-datatable .p-datatable-tbody > tr > td {
      height: 30px;
      max-width: 100%;
      color: #000000;
      font-family: Poppins;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 15px;
      padding: 0.5rem 1rem;
      font-weight: 500;
      border: 1px solid #edeff3;
      display: block;
    }
    ::ng-deep .table .p-datatable .p-datatable-tbody > tr > td .ellipses {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    ::ng-deep .pi-icon .pi {
      font-size: 0.5rem;
    }

    ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
      fill: white !important;
    }

    ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
      color: #4b3f72;
    }

    ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
      color: #4b3f72;
    }

    ::ng-deep .p-datatable .p-sortable-column.p-highlight,
    .p-datatable .p-sortable-column.p-highlight:hover {
      background: #f8f9fa;
      color: #4b3f72;
    }

    ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover {
      background: #f8f9fa;
      color: #4b3f72;
    }

    ::ng-deep .p-datatable .p-sortable-column:focus {
      box-shadow: inset 0 0 0 0.2rem #4b3f72;
      outline: 0 none;
    }
    ::ng-deep .p-paginator .p-dropdown {
      width: 91px;
    }

    ::ng-deep .p-paginator {
      display: flex !important;
      justify-content: flex-start !important;
      border: none;
      margin-top: 15px;

      .p-paginator-current {
        position: absolute;
        right: 0;
        color: #575962;
        font-size: 14px;
        letter-spacing: 0;
        font-weight: 500;
        cursor: default;
      }

      .p-paginator-rpp-options {
        margin-left: 20px;
        height: 37px;
        width: 100px;
        border-radius: 20px;
        background-color: #f4f5f8;
        border: none;
      }
    }

    @media (max-width: 500px) {
      ::ng-deep .p-paginator-current {
        bottom: 0;
      }

      ::ng-deep .p-paginator-rpp-options {
        margin-top: 10px;
      }
    }

    ::ng-deep .p-datatable .p-datatable-loading-overlay {
      top: 80px;
      background-color: transparent;
    }

    ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
      box-shadow: none;
    }

    .center-align {
      text-align: center !important;
    }

    .help-icon {
      color: #b5b5c3;
      cursor: pointer;
    }

    ::-webkit-scrollbar {
      width: 0.5em !important;
    }

    ::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
    }

    ::-webkit-scrollbar-thumb {
      background-color: darkgray !important;
      outline: 1px solid slategray !important;
    }

    .form-group .form-text {
      font-weight: 500;
    }

    ::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
      border-color: #827da0 !important;
      background: #827da0 !important;
    }

    ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
      border-color: #827da0;
    }

    ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box .p-highlight:hover {
      border-color: #827da0 !important;
      background: #827da0 !important;
    }

    ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
      border-color: #dcdcdd;
      background: #dcdcdd;
    }

    ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus {
      box-shadow: none;
    }

    ::ng-deep .p-multiselect.p-multiselect-chip .p-multiselect-token {
      background: #dcdcdd;
    }

    //   ::ng-deep .table .p-datatable-scrollable .p-frozen-column {
    //     position: sticky;
    //     background: inherit;
    //   }
    //  ::ng-deep .table .p-frozen-column {
    //   font-weight: bold;
    //   }
    //  ::ng-deep .table .p-datatable-frozen-tbody {
    //   font-weight: bold;
    //   }
    .scrollable-content {
      max-height: calc((var(--fixed-content-height, 1vh) * 100) - 100px) !important;
      overflow: hidden !important;
    }

    ::ng-deep .p-datatable-unfrozen-view .p-datatable-scrollable-body {
      overflow-y: auto !important;
    }
    ::ng-deep .p-datatable-frozen-view .p-datatable-scrollable-body {
      overflow: hidden !important;
    }
    @media (min-width: 1024px) and (max-width: 1369px) {
      .fix-col-3 {
        width: 200px;
      }
      .dynamic-col-3 {
        width: 110px;
      }
      .dynamic-col-more {
        width: 135px;
      }
      .skill-set-wrapper {
        width: 150px !important;
      }
      .employee-type-wrapper {
        width: 90px !important;
      }
    }

    @media (min-width: 1370px) and (max-width: 1500px) {
      .fix-col-3 {
        width: 210px;
      }
      .tags-wrapper {
        width: 142px;
      }
      .employee-type-wrapper {
        width: 88px !important;
      }
      .dynamic-col-3 {
        width: 140px;
      }
      .dynamic-col-more {
        width: 115px;
      }
    }

    @media (min-width: 1500px) and (max-width: 1700px) {
      .fix-col-3 {
        width: 230px;
      }
      .dynamic-col-3 {
        width: 170px;
      }
      .dynamic-col-more {
        width: 145px;
      }
      .skill-set-wrapper {
        width: 180px !important;
      }
      .employee-type-wrapper {
        width: 120px !important;
      }
    }

    @media (min-width: 1700px) and (max-width: 1800px) {
      .fix-col-3 {
        width: 230px;
      }
      .dynamic-col-3 {
        width: 190px;
      }
      .dynamic-col-more {
        width: 125px;
      }
      .employee-type-wrapper {
        width: 140px !important;
      }

      .skill-set-wrapper {
        width: 190px !important;
      }
    }
    @media (min-width: 1800px) and (max-width: 2000px) {
      .fix-col-3 {
        width: 250px;
      }
      .dynamic-col-3 {
        width: 200px;
      }
      .dynamic-col-more {
        width: 125px;
      }
      .employee-type-wrapper {
        width: 150px !important;
      }

      .skill-set-wrapper {
        width: 200px !important;
      }
    }
    @media (min-width: 2000px) {
      .fix-col-3 {
        width: 270px;
      }
      .dynamic-col-3 {
        width: 265px;
      }
      .dynamic-col-more {
        width: 150px;
      }
      .employee-type-wrapper {
        width: 150px !important;
      }

      .skill-set-wrapper {
        width: 200px !important;
      }
    }
    .background {
      height: 38px;
      border-radius: 9px 9px 0 0;
      background-color: #eeebf4;
      display: flex;
      align-items: center;
      padding-left: 0.5rem;
    }

    .PL-border {
      box-sizing: border-box;
      border: 1px solid #eeebf4;
      border-radius: 9px;
      margin-bottom: 1rem;
    }
    input[type='radio'] {
      display: none;
    }

    input[type='radio']:checked + label:before {
      background: #4b3f72;
      color: #ffffff;
      content: '\2713';
      text-align: center;
    }

    input[type='radio'] + label:before {
      border: 1px solid #4b3f72;
      border-radius: 1rem;
      content: '\00a0';
      display: inline-block;
      font: 16px/1em sans-serif;
      height: 16px;
      // margin: 0.2em 0.25em 0 0;
      padding: 0;
      vertical-align: top;
      width: 16px;
    }
    .save-filter-radio {
      display: flex;
      justify-content: flex-end;
      width: 100%;
    }
    .width-65 {
      width: 65%;
    }
  }

  ::ng-deep .confirm-dialog .p-dialog {
    width: 30vw;
  }

  ::ng-deep .filter-dialog {
    .p-component-overlay {
      background: none !important;
      animation: none !important;
    }
    .p-dialog {
      height: auto !important;
      max-height: calc(100vh - 90px) !important;
      width: 400px;
      top: 51px;

      .p-dialog-header {
        display: none;
      }

      .p-dialog-content {
        padding-top: 1rem;
      }
      .title {
        color: #757575;
        font-family: Poppins;
        font-size: 12px;
        letter-spacing: 0;
        line-height: 18px;
      }
      .share-icon {
        background-color: #4b3f72 !important;
        border-color: #4b3f72 !important;
        i,
        em {
          color: white;
        }
      }
      .filter-body {
        display: flex;
        align-content: center;
        align-items: center;
      }
      .form-check {
        height: 40px;
        background-color: white;
        display: flex;
        align-items: center;
        width: 100%;
        .form-check-label,
        .form-check-label:hover {
          cursor: pointer;
          color: black;
          width: 75%;
          color: #000000;
          font-family: Poppins;
          font-size: 12px;
          letter-spacing: 0;
          line-height: 18px;
        }
        .div {
          width: 30%;
          display: flex;
          justify-content: flex-end;
        }
      }

      .filter-icons {
        display: flex;
        width: 100%;
        justify-content: flex-end;
        a {
          margin: 0 3px;
        }
      }
    }
  }
  .form-group {
    padding-bottom: 0;
    &:first-child {
      padding-top: 0;
    }
  }
  ::ng-deep .dropdown .p-dropdown,
  ::ng-deep .dropdown .p-dropdown .p-focus {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    box-shadow: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
    padding: 1.2rem 0rem;
  }

  ::ng-deep .dropdown .p-dropdown .p-dropdown-label {
    color: #000000;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: -0.32px;
    line-height: 25px;
  }

  ::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder {
    color: #b5b5c3 !important;
  }
  ::ng-deep .p-datatable .p-datatable-loading-overlay {
    top: 29px;
    background-color: transparent;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  ::ng-deep .custom-pop.popover {
    max-width: 100% !important;
    min-width: 500px;
    min-height: 160px !important;
    max-height: 350px !important;
    overflow: auto !important;
    margin-bottom: 0rem;
    .arrow {
      bottom: 0rem !important;
      right: 0rem !important;
    }
  }
  ::ng-deep .p-datatable .p-datatable-thead tr > th:first-child,
  ::ng-deep .p-datatable .p-datatable-tbody tr > td:first-child {
    padding-left: 1rem;
  }

  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    height: 20px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    padding: 0.5rem 0.5rem;
    background-color: #ecedf6 !important;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    height: 20px;
    width: 123px;
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 0.5rem;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  ::ng-deep .bench-report-wrapper {
    .p-datatable-wrapper {
      max-height: 100% !important;
      // min-height: 230px !important;
    }
  }
  ::ng-deep .active {
    .p-datatable-wrapper {
      height: 100% !important;
    }
  }

  ::ng-deep .dialog-applied-tags .p-dialog {
    width: 460px;
  }

  ::ng-deep .cursor-pointer .tag-count .p-badge {
    background-color: $primary;
  }

  .filter-tags {
    ::ng-deep .p-treeselect {
      width: 100% !important;
    }
  }

  .employee-type-wrapper {
    width: 110px !important;
  }

  .skill-set-wrapper {
    width: 170px !important;
  }

  .w-50 {
    width: 50%;
  }

  .calendar-filter-panel {
    display: flex;
    justify-content: space-between;
  }

  .calendar-filter-body {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .calendar-cust-btn {
    color: #4b3f72 !important;
  }

  .calendar-cust-btn:hover {
    color: #fff !important;
  }

  .md-resource-header-template-name {
    height: 100%;
    display: block;
    padding: 0 5px;
    float: right;
    width: 50%;
    line-height: 14px;
    border-left: 1px solid #ccc;
  }

  .md-resource-header-template-seats {
    display: block;
    width: 50%;
    height: 100%;
    float: left;
    line-height: 14px;
    padding: 0 5px;
  }

  .md-resource-header-template-title {
    font-weight: 600;
    line-height: 56px;
  }

  .calendar-cust-save-btn {
    margin-right: 15px;
    color: #fff !important;
  }

  .calendar-view-wrapper {
    max-height: 87vh;
  }

  .btn-switcher {
    border-color: transparent;
    display: flex;
    justify-content: flex-end;
  }

  .btn-switcher.btn-right {
    border-radius: 0 6px 6px 0;

    svg {
      background: #574985;
    }
  }

  .btn-switcher.btn-left {
    border-radius: 6px 0 0 6px;

    svg {
      background: #574985;
    }
  }

  .btn-switcher.switch-active {
    background: #574985;

    svg {
      background: #fff;
    }
  }
  ::ng-deep .mbsc-popup-center {
    max-width: 750px !important;
  }

  ::ng-deep .mbsc-popup-body {
    min-height: 160px;
    min-width: 600px;
    background: $white !important;
  }

  .md-tooltip-info .project-name-wrapper {
    color: $primary !important;
    text-decoration: none !important;
    background-color: transparent !important;
  }

  .allocation-wrapper {
    ::ng-deep .p-dropdown-panel .p-dropdown-items .p-dropdown-item {
      font-size: 16px;
      .form-control {
        height: 2.3em;
        font-size: 14px !important;
      }
    }
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }

  .custome-p-table-spinner {
    position: relative;
    z-index: 999999;
    transform: translate(-38rem, 25rem);
  }

  .apply-filter-msg {
    font-size: 14px;
    font-weight: 500;
    background-color: #ffffff;
  }

  ::ng-deep .export-dialog {
    .p-dialog {
      height: 128px;
      width: 170px;
      top: 51px;
      right: 155px;

      .p-dialog-header {
        display: none;
      }
    }
    .p-dialog .p-dialog-content {
      padding: 0 5px;
    }
  }

  ::ng-deep .download svg {
    width: 26px !important;
    height: 26px !important;
  }

  .export-action-listing {
    display: flex;
    align-items: center;
    flex-direction: column;
    button {
      width: 100%;
      background-color: #f1f1f1;
      color: #000;
      &:hover {
        background-color: #b9b9b9;
        color: #000;
      }
      ::ng-deep .p-button-icon {
        font-size: 1.2rem;
        margin-left: 1rem;
      }
    }
  }
  .bench {
    ::ng-deep .popup-column {
      z-index: 100 !important;
      position: absolute !important;
      right: 5px !important;
      top: 100px !important;
    }
  }

  ::ng-deep .p-datatable-scrollable-both .p-datatable-thead > tr > th,
  .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-both .p-datatable-thead > tr > th {
    flex: 1 1 0 !important;
  }

  ::ng-deep .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td {
    flex: 1 1 0 !important;
  }
}

.example-container {
  position: relative;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
}

.benchReportWrapper {
  ::ng-deep .mat-drawer.mat-drawer-end {
    transform: none !important;
  }

  .fix-dialog {
    width: 50vw !important;
    // @extend .confirm-dialog;
  }
}
