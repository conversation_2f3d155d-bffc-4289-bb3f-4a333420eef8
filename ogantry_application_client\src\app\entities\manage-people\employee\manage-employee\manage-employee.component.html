<div class="card card-custom gutter-b" id="manageEmployeeScreen">
  <app-card-header
    [cardTitle]="cardTitle"
    [cardSubTitle]="cardSubTitle"
    [buttons]="buttons"
    [showSplitButton]="true"
    [splitButtonDropDownOption]="splitButtonDropDownOption"
  ></app-card-header>
  <div>
    <div class="float-left py-2 px-2">
      <div class="d-inline ml-2" *ngIf="checkedEmployee?.length">
        <span title="Add Tag" [inlineSVG]="'assets/media/svg/icons/add-tag.svg'" cacheSVG="true" class="plus-icon pointer" (click)="addTagsToMultipleProject()"> </span>
      </div>
    </div>
    <span (clickOutside)="isShowHideColumns ? (isShowHideColumns = false) : ''">
      <div class="float-right mr-4 py-2 pointer" (click)="isShowHideColumns = !isShowHideColumns" #coulmnToggel>
        <app-filter-icon-shared></app-filter-icon-shared>
      </div>
      <div class="popup-column card">
        <app-filter-table-fields
          *ngIf="isShowHideColumns"
          [selectedColumns]="selectedColumns"
          [frozenCols]="frozenCols"
          dynamicBindingKey="monthLabel"
          (onSelectColumChange)="onSelectColumnsChange($event)"
        ></app-filter-table-fields>
      </div>
    </span>
  </div>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [(selection)]="checkedEmployee"
      [resizableColumns]="true"
      [value]="employeeData"
      [lazy]="true"
      (onLazyLoad)="getEmployeeList($event)"
      (onPage)="pageChange()"
      [rows]="10"
      [showCurrentPageReport]="true"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="[10, 25, 50]"
      [scrollable]="true"
      selectionMode="multiple"
      [loading]="loading"
      [paginator]="loading ? false : showPaginator ? true : false"
      currentPageReportTemplate="Displaying {first} - {last} of {totalRecords} records"
      (onSort)="sortColumn()"
      [filterDelay]="0"
      [sortField]="sortFieldName"
      [sortOrder]="sortOrderNumber"
      (selectionChange)="listChecked()"
      [selectAll]="true"
    >
      <ng-template pTemplate="header">
        <tr class="sticky-row-1">
          <th class="checkbox-wrapper" style="max-width: 42px !important">
            <div>
              <p-checkbox
                [(ngModel)]="listSelected"
                [binary]="true"
                checkboxIcon="pi pi-minus"
                (ngModelChange)="listSelected ? selectAllEmployeeCheck() : removeEmployee()"
              ></p-checkbox>
            </div>
          </th>
          <th *ngIf="_pCols.includes('first_name')" id="first_name" pSortableColumn="first_name" class="header-width-firstname no-wrap" pResizableColumn>
            First Name<p-sortIcon field="first_name"></p-sortIcon>
          </th>
          <th *ngIf="_pCols.includes('last_name')" id="last_name" pSortableColumn="last_name" class="header-width-lastname no-wrap" pResizableColumn>
            Last Name<p-sortIcon field="last_name"></p-sortIcon>
          </th>
          <th *ngIf="_pCols.includes('email')" id="email" pSortableColumn="email" class="header-width-email no-wrap" pResizableColumn>
            Email<p-sortIcon field="email"></p-sortIcon>
          </th>
          <th *ngIf="_pCols.includes('skill_set')" id="position" class="header-width-skillset no-wrap" pResizableColumn>Skill Set</th>
          <th *ngIf="_pCols.includes('start_date')" id="start_date" pSortableColumn="start_date" class="header-width-start no-wrap" pResizableColumn>
            Start Date<p-sortIcon field="start_date"></p-sortIcon>
          </th>
          <!-- <th id="e_name" class="header-width-avail text-number-right" pSortableColumn="daily_billable_hours">
            Availability<p-sortIcon field="daily_billable_hours"></p-sortIcon>
          </th> -->
          <th *ngIf="_pCols.includes('type')" id="type" class="header-width-type no-wrap" pSortableColumn="employee_type_name" pResizableColumn>
            Type<p-sortIcon field="employee_type_name"></p-sortIcon>
          </th>
          <th *ngIf="_pCols.includes('cost')" id="cost" class="header-width-cost text-number-right no-wrap" pSortableColumn="hourly_cost" pResizableColumn>
            Cost<p-sortIcon field="hourly_cost"></p-sortIcon>
          </th>
          <th *ngIf="_pCols.includes('status')" id="status" class="header-width-region no-wrap" pSortableColumn="employee_status" pResizableColumn>
            Status<p-sortIcon field="employee_status"></p-sortIcon>
          </th>
          <th *ngIf="_pCols.includes('tags')" id="tags" class="tags-width-wrapper no-wrap" pResizableColumn>Tags</th>

          <ng-container *ngFor="let item of extendFields; let index = index">
            <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
              <ng-container *ngIf="filed?.component == componentType">
                <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                  <th colspan="1" *ngIf="checkSelectedColumn(filedDetails?.name)" class="no-wrap extended-field-header">
                    {{ filedDetails?.name }}
                  </th>
                </ng-container>
              </ng-container>
            </ng-container>
          </ng-container>

          <th id="actions" class="header-width-actions text-center no-wrap">Actions</th>
        </tr>
        <tr class="sticky-row-2" *ngIf="showFilter">
          <th class="checkbox-wrapper" style="max-width: 42px !important"></th>
          <th *ngIf="_pCols.includes('first_name')" id="searchFisrtName" class="header-width-firstname">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.first_name_search" (click)="clearFilter('first_name_search')"></em>
              <input pInputText type="text" class="p-column-filter" placeholder="First Name" [(ngModel)]="dataFilter.first_name_search" (input)="filter()" />
            </span>
          </th>
          <th *ngIf="_pCols.includes('last_name')" id="searchLastName" class="header-width-lastname">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.last_name_search" (click)="clearFilter('last_name_search')"></em>
              <input pInputText type="text" class="p-column-filter" placeholder="Last Name" [(ngModel)]="dataFilter.last_name_search" (input)="filter()" />
            </span>
          </th>
          <th *ngIf="_pCols.includes('email')" id="searchEmail" class="header-width-email">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.email_search" (click)="clearFilter('email_search')"></em>
              <input pInputText type="text" class="p-column-filter" placeholder="Email" [(ngModel)]="dataFilter.email_search" (input)="filter()" />
            </span>
          </th>
          <th *ngIf="_pCols.includes('skill_set')" id="searchPosition" class="header-width-skillset">
            <p-dropdown
              appendTo="body"
              [options]="employeeRoles"
              [(ngModel)]="dataFilter.position_types"
              styleClass="p-column-filter pi-icon"
              placeholder="Select"
              (onChange)="filter()"
            >
            </p-dropdown>
          </th>
          <th *ngIf="_pCols.includes('start_date')" id="searchStartDate" class="header-width-start">
            <p-calendar
              appendTo="body"
              placeholder="Start Date"
              [(ngModel)]="dataFilter.start_date"
              showButtonBar="true"
              (onClearClick)="clearStartDate()"
              [readonlyInput]="true"
              (onSelect)="filter()"
            ></p-calendar>
          </th>
          <!-- <th id="searchAvailability">
            <span class="p-input-icon-right">
              <em
                class="pi pi-times"
                *ngIf="dataFilter.daily_billable_hours"
                (click)="clearFilter('daily_billable_hours')"
              ></em>
              <input
                sflIsNumber
                pInputText
                type="text"
                class="p-column-filter"
                placeholder="Daily Hrs"
                [(ngModel)]="dataFilter.daily_billable_hours"
                (input)="filter()"
              />
            </span>
          </th> -->
          <th *ngIf="_pCols.includes('type')" id="searchType" class="header-width-type">
            <p-dropdown
              appendTo="body"
              [options]="employeeTypes"
              [(ngModel)]="dataFilter.employee_type_name"
              styleClass="p-column-filter pi-icon"
              placeholder="Select"
              (onChange)="filter()"
            >
            </p-dropdown>
          </th>
          <th *ngIf="_pCols.includes('cost')" id="searchCost" class="header-width-cost text-number-right">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.hourly_cost" (click)="clearFilter('hourly_cost')"></em>
              <input sflIsNumber pInputText type="text" class="p-column-filter" placeholder="$ 0.00" [(ngModel)]="dataFilter.hourly_cost" (input)="filter()" />
            </span>
          </th>
          <th *ngIf="_pCols.includes('status')" id="status" class="header-width-region">
            <p-dropdown
              appendTo="body"
              [options]="allEmployeeTypes"
              [(ngModel)]="dataFilter.employee_status"
              (onChange)="filter()"
              styleClass="p-column-filter pi-icon"
              placeholder="Select"
              optionLabel="label"
              optionValue="value"
            >
            </p-dropdown>
          </th>
          <th *ngIf="_pCols.includes('tags')" class="tags-width-wrapper">
            <p-treeSelect
              appendTo="body"
              class="search-tag-wrapper"
              [(ngModel)]="selectedTags"
              (ngModelChange)="tagSelected($event)"
              [options]="groupedCategory?.data"
              display="chip"
              [metaKeySelection]="false"
              selectionMode="checkbox"
              placeholder="Select Tags"
              [ngModelOptions]="{ standalone: true }"
              filterBy="label"
            >
            </p-treeSelect>
          </th>

          <ng-container *ngFor="let item of extendFields; let index = index">
            <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
              <ng-container *ngIf="filed?.component == componentType">
                <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                  <th colspan="1" *ngIf="checkSelectedColumn(filedDetails?.name)" [id]="filedDetails?.name">
                    <span class="p-input-icon-right" *ngIf="filedDetails.DBTag">
                      <em class="pi pi-times" *ngIf="getFilterValue(filedDetails?.DBTag)" (click)="deleteExtendFiledFilter(filedDetails?.DBTag)"></em>
                      <input
                        pInputText
                        type="text"
                        [value]="getFilterValue(filedDetails?.DBTag)"
                        (input)="filterExtendFiled(filedDetails?.DBTag, $event?.target)"
                        class="p-column-filter"
                        [placeholder]="filedDetails?.name"
                      />
                    </span>
                  </th>
                </ng-container>
              </ng-container>
            </ng-container>
          </ng-container>

          <th class="header-width-actions"></th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-employee>
        <tr>
          <td class="table-checkbox">
            <p-tableCheckbox [value]="employee"></p-tableCheckbox>
          </td>
          <td *ngIf="_pCols.includes('first_name')" class="header-width-firstname">
            <span [title]="employee?.employee?.first_name">
              {{ employee?.employee?.first_name }}
            </span>
          </td>
          <td *ngIf="_pCols.includes('last_name')" class="header-width-lastname">
            <span [title]="employee?.employee?.last_name">
              {{ employee?.employee?.last_name }}
            </span>
          </td>
          <td *ngIf="_pCols.includes('email')" class="header-width-email">
            <span [title]="employee?.employee?.email">
              {{ employee?.employee?.email }}
            </span>
          </td>
          <td *ngIf="_pCols.includes('skill_set')" class="header-width-skillset">
            <span [title]="getSkillSet(employee?.employee?.position_types)">
              {{ getSkillSet(employee?.employee?.position_types) }}
            </span>
          </td>

          <td *ngIf="_pCols.includes('start_date')" class="text-number-right header-width-start">
            <div>
              {{ employee?.employee?.start_date | date : 'MM/dd/yyyy' }}

              <ng-template #popOver>
                <div *isFetchingData="loading$">
                  <div [ngStyle]="styleObj.heading">
                    {{ 'End Date' }}
                  </div>
                  <div [ngStyle]="styleObj.subHeading">
                    {{ getDate(employee?.employee?.end_date) }}
                  </div>
                </div>
              </ng-template>

              <div *ngIf="employee?.employee?.inactive" class="display">
                <a type="button" popoverClass="my-custom-class" class="icon-style" placement="right" container="body" [ngbPopover]="popOver">
                  <fa-icon [icon]="'info-circle'" class="ml-1 help-icon"></fa-icon>
                </a>
              </div>
            </div>
          </td>
          <!-- <td class="text-number-right"> {{ employee?.employee?.daily_billable_hours }} hrs</td> -->
          <td *ngIf="_pCols.includes('type')" class="header-width-type">
            {{ employee?.employee?.employee_type?.name }}
          </td>
          <td *ngIf="_pCols.includes('cost')" class="header-width-cost">
            <span [ngClass]="{ 'mr-2': isEmployeeFutureFinancial(employee?.employee?.start_date) }">${{ employee?.employee?.hourly_cost }}</span>
            <span
              *ngIf="isEmployeeFutureFinancial(employee?.employee?.start_date)"
              class="info-icon"
              [ngbTooltip]="tooltipContent"
              tooltipClass="future-financial-tooltip"
              placement="auto"
              container="body"
            >
              <fa-icon [icon]="'exclamation-triangle'"></fa-icon>
            </span>
            <ng-template #tooltipContent>
              <div class="tooltip-content">
                <span>{{
                  'Warning: This employee has changes in the future that effect their financials.You can view the changes in the Financial History Section on the Employee Details screen.'
                }}</span>
              </div>
            </ng-template>
          </td>
          <td *ngIf="_pCols.includes('status')" class="header-width-region">
            {{ employee?.status }}
          </td>
          <td *ngIf="_pCols.includes('tags')" class="tags-width-wrapper">
            <ng-container *ngIf="employee?.employee?.tags.length">
              <span class="ellipses">
                <span class="taglist">
                  <p-chip *ngFor="let tag of employee?.employee?.tags; let i = index" (click)="openTagModal(employee?.employee?.tags)" class="cursor-pointer">
                    <span class="tooltip-hover" [ngbTooltip]="categoryDetails" #t2="ngbTooltip" (mouseenter)="toggleWithCategory(t2, tag)">{{ getTagsCount(tag, i < 2) }}</span>
                  </p-chip>
                </span>
              </span>
              <span class="count cursor-pointer" *ngIf="employee?.employee?.tags.length > 2" (click)="openTagModal(employee?.employee?.tags)">
                <span class="tag-count">
                  <p-badge [value]="getTagCount(employee?.employee?.tags)"></p-badge>
                </span>
              </span>
            </ng-container>
          </td>

          <ng-container *ngFor="let item of extendFields; let index = index">
            <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
              <ng-container *ngIf="filed?.component == componentType">
                <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                  <td
                    class="show-pointer"
                    colspan="1"
                    *ngIf="checkSelectedColumn(filedDetails?.name)"
                    [id]="filedDetails?.name"
                    (click)="openExtendFiledPopup(employee, filedDetails)"
                    [ngClass]="{ 'dynamic-text text-truncate': filedDetails.type === filedType.Text_Area }"
                  >
                    {{ getValueByPartialKey(filedDetails?.DBTag, employee?.employee?.extended_fields, filedDetails?.type) }}
                  </td>
                </ng-container>
              </ng-container>
            </ng-container>
          </ng-container>

          <td class="justify-content-center header-width-actions" colspan="1">
            <div ngbDropdown class="d-inline-block" container="body">
              <button class="btn btn-clean btn-sm btn-icon btn-icon-md btn-expand text-center" id="dropdownBasic1" ngbDropdownToggle>
                <em class="flaticon-more"></em>
              </button>
              <div ngbDropdownMenu aria-labelledby="dropdownBasic1" isOpen="false" placement="left">
                <button ngbDropdownItem [routerLink]="[appRoutes.EDIT_EMPLOYEE, employee?.employee?.id]">
                  {{ employee?.employee?.inactive ? 'View ' : 'Edit ' }}
                  Employee Details
                </button>
                <button ngbDropdownItem [routerLink]="[appRoutes.EMPLOYEE_COST, employee?.employee?.id]" [disabled]="employee?.employee?.inactive">Update Employee Cost</button>
                <button ngbDropdownItem [routerLink]="[appRoutes.UTILIZATION_COST, employee?.employee?.id]" [disabled]="employee?.employee?.inactive">
                  Update Utilization Target
                </button>
                <button ngbDropdownItem [routerLink]="[appRoutes.TERMINATE_EMPLOYEE, employee?.employee?.id]" [disabled]="employee?.employee?.inactive">Terminate Employee</button>
                <button ngbDropdownItem (click)="viewStaffedPosition(employee)" [disabled]="employee?.employee?.inactive">View Staffed Positions</button>
                <button ngbDropdownItem (click)="redirectToMangeTime(employee)" [disabled]="employee?.employee?.inactive">View Time</button>
              </div>
            </div>
          </td>
          <!-- <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary" *hasAnyPermission="permissionModules.MANAGE_EMPLOYEE;disableEvent:true"
              [routerLink]="[appRoutes.EDIT_EMPLOYEE, employee?.employee?.id]">
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md">
              </span>
            </a> -->
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="12" class="center-align">No Employees found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<ng-template #categoryDetails let-tag="tag">
  <p [innerHTML]="getTagCategorySubCategory(tag)"></p>
</ng-template>

<p-dialog header="Applied Tags" [(visible)]="showTagDialog" [modal]="true" class="dialog-applied-tags" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <ul>
    <li *ngFor="let tag of selectedTagToView">
      <span [ngbTooltip]="categoryDetails" #t3="ngbTooltip" (mouseenter)="toggleWithCategory(t3, tag)">{{ getExtractedTags(tag) }}</span>
    </li>
  </ul>
</p-dialog>

<p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div class="form-group first" *ngIf="editFilterObj">
    <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
    <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Delete" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<ng-template #ColumnFilter>
  <app-filter-table-fields [selectedColumns]="selectedColumns" (onChange)="onSelectColumnsChange($event)"></app-filter-table-fields>
</ng-template>

<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showSavedFilter"
  [modal]="true"
  class="filter-dialog-manage-employee"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="filter-listing">
      <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
      <div *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true" class="title">Shared Filters</div>
      <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
        <span *ngIf="sharedFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of sharedFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons">
              <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                  <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                  <em class="fa-solid fa-share" title="UnShare Filter"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
                </a>
              </ng-container>
            </div>
          </div>
        </span>
      </ng-container>
      <ng-template #noData>
        <div>-</div>
      </ng-template>
      <div class="title">My Filters</div>
      <span *ngIf="myFilters?.length; else noData">
        <div
          class="form-check filter-body"
          *ngFor="let filterOption of myFilters"
          [ngClass]="{
            'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
          }"
        >
          <label class="form-check-label">
            <input
              [formControl]="selectedFilterFormControl"
              (ngModelChange)="applySelectedFilterAndUpdateUrl()"
              type="radio"
              class="form-check-input custom-radio"
              [value]="filterOption"
              name="filteroption"
            />{{ filterOption?.query_filter?.name }}
          </label>
          <div class="filter-icons">
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
              <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              (click)="shareFilter(filterOption)"
              *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
            >
              <em class="fa-solid fa-share" title="Share Filter"></em>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
            </a>
          </div>
        </div>
      </span>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [header]="'UPDATE ' + updateExtendFiled"
  [(visible)]="showUpdateExtendFiledDialog"
  [modal]="true"
  class="description-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '40vw' }"
  (onHide)="closeExtendFiledPopup()"
>
  <app-extended-form
    [extendFieldsObj]="employeeObj?.employee?.extended_fields"
    [filedName]="updateExtendFiled"
    [componentType]="componentType"
    [isShowRequiredError]="true"
    #childForm
  >
  </app-extended-form>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeExtendFiledPopup()">Cancel</button>
      <button type="button" class="btn-save" (click)="onSaveExtendedEdit()" [isSubmitting]="isSubmitting" [disabled]="checkExtendedFormValidity()">Save</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [header]="'Hyper link navigation'"
  [(visible)]="showHyperlinkNavigationDialog"
  [modal]="true"
  class="description-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '40vw' }"
  (onHide)="showHyperlinkNavigationDialog = false"
>
  <div class="mb-2">
    <h6 class="mb-1">
      Hyper link: <b>{{ linkValue }}</b>
    </h6>
    <div>Do you want to open the link in a new tab, or update the current link?</div>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="openExtendFiledPopup(employeeObj, fieldDetail, true)">Update existing link</button>
      <button type="button" class="btn-save" (click)="navigateOnLink()">Open link in new tab</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showExportOptionDialog"
  [modal]="true"
  class="export-dialog"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="export-action-listing" *ngIf="showExportOptions">
      <button pButton class="btn p-button-text mb-2 mt-2" icon="pi pi-file-o" iconPos="left" (click)="exportReport('csv')">Export CSV</button>
      <button pButton class="btn p-button-text mb-2" icon="pi pi-file-pdf" iconPos="left" (click)="exportReport('pdf')">Export PDF</button>
      <button pButton class="btn p-button-text mb-2" icon="pi pi-file-excel" iconPos="left" (click)="exportReport('excel')">Export Excel</button>
    </div>
  </ng-template>
</p-dialog>
