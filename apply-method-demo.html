<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding .apply() Method</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .output {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .step {
            background: #d1ecf1;
            border-left: 4px solid #bee5eb;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Understanding the .apply() Method</h1>
        
        <div class="step">
            <h3>What is .apply()?</h3>
            <p>The <span class="highlight">.apply()</span> method calls a function with:</p>
            <ul>
                <li><strong>A specific 'this' context</strong> - what 'this' refers to inside the function</li>
                <li><strong>An array of arguments</strong> - the parameters to pass to the function</li>
            </ul>
            <div class="code-block">
                functionName.apply(thisContext, [arg1, arg2, arg3])
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Interactive Examples</h2>
        
        <button onclick="basicExample()">1. Basic .apply() Example</button>
        <button onclick="thisContextExample()">2. 'this' Context Example</button>
        <button onclick="decoratorExample()">3. Why Decorators Need .apply()</button>
        <button onclick="comparisonExample()">4. .apply() vs .call() vs Direct</button>
        <button onclick="clearOutput()">Clear Output</button>
        
        <div class="output" id="output">Click a button above to see examples!</div>
    </div>

    <div class="container">
        <h2>📚 Step-by-Step Breakdown</h2>
        
        <div class="step">
            <h4>Step 1: Function without 'this'</h4>
            <div class="code-block">
function add(a, b) {
    return a + b;
}

// These are equivalent:
add(5, 3)              // Normal call
add.apply(null, [5, 3]) // Using apply
            </div>
        </div>

        <div class="step">
            <h4>Step 2: Function with 'this'</h4>
            <div class="code-block">
function greet(message) {
    return message + this.name;
}

const person = { name: "Alice" };

// Set 'this' to person object:
greet.apply(person, ["Hello, "]) // "Hello, Alice"
            </div>
        </div>

        <div class="step">
            <h4>Step 3: In Decorators</h4>
            <div class="code-block">
function LogMethod(target, propertyName, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args) {
        console.log("Before method");
        
        // ✅ RIGHT: Preserve 'this' context
        const result = originalMethod.apply(this, args);
        
        // ❌ WRONG: Lose 'this' context
        // const result = originalMethod(args[0], args[1]);
        
        console.log("After method");
        return result;
    };
}
            </div>
        </div>
    </div>

    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            output.textContent += message + '\n';
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            output.textContent = '';
        }

        function basicExample() {
            log('=== BASIC .apply() EXAMPLE ===');
            
            // Simple function
            function multiply(a, b) {
                return a * b;
            }
            
            // Normal call
            const result1 = multiply(4, 5);
            log(`Normal call: multiply(4, 5) = ${result1}`);
            
            // Using apply
            const result2 = multiply.apply(null, [4, 5]);
            log(`Using apply: multiply.apply(null, [4, 5]) = ${result2}`);
            
            log(`Both results are the same: ${result1 === result2}`);
            log('');
        }

        function thisContextExample() {
            log('=== THIS CONTEXT EXAMPLE ===');
            
            function introduce(greeting) {
                return `${greeting}, I am ${this.name} and I am ${this.age} years old`;
            }
            
            const person1 = { name: 'Alice', age: 25 };
            const person2 = { name: 'Bob', age: 30 };
            
            // Using apply with different 'this' contexts
            const intro1 = introduce.apply(person1, ['Hello']);
            const intro2 = introduce.apply(person2, ['Hi']);
            
            log('Same function, different contexts:');
            log(`Person 1: ${intro1}`);
            log(`Person 2: ${intro2}`);
            log('');
        }

        function decoratorExample() {
            log('=== WHY DECORATORS NEED .apply() ===');
            
            class Calculator {
                constructor(name) {
                    this.name = name;
                }
                
                add(a, b) {
                    return `${this.name} calculated: ${a} + ${b} = ${a + b}`;
                }
            }
            
            // Decorator that preserves 'this' context
            function LogMethod(target, propertyName, descriptor) {
                const originalMethod = descriptor.value;
                
                descriptor.value = function(...args) {
                    log(`🔍 Calling ${propertyName} with args: [${args.join(', ')}]`);
                    
                    // ✅ Using apply preserves 'this.name'
                    const result = originalMethod.apply(this, args);
                    
                    log(`✅ Result: ${result}`);
                    return result;
                };
            }
            
            // Apply decorator manually
            LogMethod(Calculator.prototype, 'add', 
                Object.getOwnPropertyDescriptor(Calculator.prototype, 'add'));
            
            const calc = new Calculator('SuperCalc');
            calc.add(10, 5);
            
            log('Notice how "this.name" works correctly because we used .apply()');
            log('');
        }

        function comparisonExample() {
            log('=== COMPARISON: .apply() vs .call() vs Direct ===');
            
            function calculate(operation, a, b) {
                const result = operation === 'add' ? a + b : a * b;
                return `${this.calculator}: ${a} ${operation === 'add' ? '+' : '*'} ${b} = ${result}`;
            }
            
            const context = { calculator: 'MathBot' };
            
            // Method 1: Direct call (no custom 'this')
            try {
                const direct = calculate('add', 3, 4);
                log(`Direct call: ${direct}`);
            } catch (e) {
                log(`Direct call failed: ${e.message}`);
            }
            
            // Method 2: Using .call() (individual arguments)
            const withCall = calculate.call(context, 'add', 3, 4);
            log(`Using .call(): ${withCall}`);
            
            // Method 3: Using .apply() (array of arguments)
            const withApply = calculate.apply(context, ['multiply', 3, 4]);
            log(`Using .apply(): ${withApply}`);
            
            log('');
            log('Key differences:');
            log('• .call(thisContext, arg1, arg2, arg3)');
            log('• .apply(thisContext, [arg1, arg2, arg3])');
            log('• .apply() is perfect for decorators because we have ...args array');
            log('');
        }
    </script>
</body>
</html>
