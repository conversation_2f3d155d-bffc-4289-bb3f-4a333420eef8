<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Decorator Tutorial Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Decorator Tutorial - Interactive Demo</h1>
        
        <div class="section">
            <h2>What You'll Learn:</h2>
            <ul>
                <li>What decorators are and how they work</li>
                <li>Method decorators with logging</li>
                <li>Property decorators with validation</li>
                <li>Class decorators for enhancement</li>
                <li>Practical examples: timing, caching, validation</li>
            </ul>
        </div>

        <div class="section">
            <h2>🧪 Try the Examples:</h2>
            <button onclick="runBasicExample()">1. Basic Method Decorator</button>
            <button onclick="runPropertyExample()">2. Property Decorator</button>
            <button onclick="runTimingExample()">3. Timing Decorator</button>
            <button onclick="runValidationExample()">4. Validation Decorator</button>
            <button onclick="runCachingExample()">5. Caching Decorator</button>
            <button onclick="clearOutput()">Clear Output</button>
        </div>

        <div class="output" id="output">Click a button above to see decorator examples in action!</div>
    </div>

    <script>
        // Enable experimental decorators for this demo
        const output = document.getElementById('output');
        
        function log(message) {
            output.textContent += message + '\n';
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            output.textContent = '';
        }

        // Example 1: Basic Method Decorator
        function LogMethod(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;
            descriptor.value = function(...args) {
                log(`🔍 Calling method: ${propertyName} with arguments: [${args.join(', ')}]`);
                const result = originalMethod.apply(this, args);
                log(`✅ Method ${propertyName} returned: ${result}`);
                return result;
            };
        }

        // Example 2: Property Decorator (simplified for demo)
        function LogProperty(target, propertyName) {
            let value;
            Object.defineProperty(target, propertyName, {
                get: function() {
                    log(`📖 Getting property ${propertyName}: ${value}`);
                    return value;
                },
                set: function(newValue) {
                    log(`📝 Setting property ${propertyName} to: ${newValue}`);
                    value = newValue;
                }
            });
        }

        // Example 3: Timing Decorator
        function Timing(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;
            descriptor.value = function(...args) {
                const startTime = Date.now();
                const result = originalMethod.apply(this, args);
                const timeTaken = Date.now() - startTime;
                log(`⏱️ Method ${propertyName} took ${timeTaken}ms to execute`);
                return result;
            };
        }

        // Example 4: Validation Decorator
        function ValidatePositive(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;
            descriptor.value = function(...args) {
                for (let i = 0; i < args.length; i++) {
                    if (typeof args[i] !== 'number' || args[i] <= 0) {
                        throw new Error(`❌ Argument ${i + 1} must be a positive number`);
                    }
                }
                log(`✅ Validation passed for ${propertyName}`);
                return originalMethod.apply(this, args);
            };
        }

        // Example 5: Caching Decorator
        function Cache(target, propertyName, descriptor) {
            const originalMethod = descriptor.value;
            const cache = new Map();
            descriptor.value = function(...args) {
                const cacheKey = JSON.stringify(args);
                if (cache.has(cacheKey)) {
                    log(`💾 Cache hit for ${propertyName} with args: [${args.join(', ')}]`);
                    return cache.get(cacheKey);
                }
                log(`🔄 Cache miss for ${propertyName}, calculating...`);
                const result = originalMethod.apply(this, args);
                cache.set(cacheKey, result);
                return result;
            };
        }

        // Demo Functions
        function runBasicExample() {
            log('=== BASIC METHOD DECORATOR EXAMPLE ===');
            
            class Calculator {
                add(a, b) {
                    return a + b;
                }
            }
            
            // Apply decorator manually for demo
            LogMethod(Calculator.prototype, 'add', Object.getOwnPropertyDescriptor(Calculator.prototype, 'add'));
            
            const calc = new Calculator();
            calc.add(5, 3);
            calc.add(10, 7);
            log('');
        }

        function runPropertyExample() {
            log('=== PROPERTY DECORATOR EXAMPLE ===');
            
            class Person {
                constructor() {
                    LogProperty(this, 'name');
                    LogProperty(this, 'age');
                }
            }
            
            const person = new Person();
            person.name = 'Alice';
            person.age = 25;
            log(`Final name: ${person.name}`);
            log(`Final age: ${person.age}`);
            log('');
        }

        function runTimingExample() {
            log('=== TIMING DECORATOR EXAMPLE ===');
            
            class DataProcessor {
                processArray(data) {
                    let sum = 0;
                    for (let i = 0; i < data.length; i++) {
                        sum += data[i] * 2;
                    }
                    return sum;
                }
            }
            
            Timing(DataProcessor.prototype, 'processArray', Object.getOwnPropertyDescriptor(DataProcessor.prototype, 'processArray'));
            
            const processor = new DataProcessor();
            const testData = Array.from({length: 100000}, (_, i) => i);
            const result = processor.processArray(testData);
            log(`Result: ${result}`);
            log('');
        }

        function runValidationExample() {
            log('=== VALIDATION DECORATOR EXAMPLE ===');
            
            class MathOperations {
                divide(a, b) {
                    return a / b;
                }
            }
            
            ValidatePositive(MathOperations.prototype, 'divide', Object.getOwnPropertyDescriptor(MathOperations.prototype, 'divide'));
            
            const math = new MathOperations();
            
            try {
                log(`10 ÷ 2 = ${math.divide(10, 2)}`);
                log(`Trying 10 ÷ (-2)...`);
                math.divide(10, -2);
            } catch (error) {
                log(`Error caught: ${error.message}`);
            }
            log('');
        }

        function runCachingExample() {
            log('=== CACHING DECORATOR EXAMPLE ===');
            
            class ExpensiveCalculations {
                fibonacci(n) {
                    if (n <= 1) return n;
                    return this.fibonacci(n - 1) + this.fibonacci(n - 2);
                }
            }
            
            Cache(ExpensiveCalculations.prototype, 'fibonacci', Object.getOwnPropertyDescriptor(ExpensiveCalculations.prototype, 'fibonacci'));
            
            const calc = new ExpensiveCalculations();
            log('First call to fibonacci(10):');
            log(`Result: ${calc.fibonacci(10)}`);
            log('Second call to fibonacci(10):');
            log(`Result: ${calc.fibonacci(10)}`);
            log('');
        }
    </script>
</body>
</html>
