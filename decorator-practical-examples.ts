// ========================================
// PRACTICAL DECORATOR EXAMPLES
// ========================================

// ========================================
// EXAMPLE 1: TIMING DECORATOR
// ========================================

// This decorator measures how long a method takes to execute
function Timing(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args: any[]) {
        // Record start time
        const startTime = Date.now();
        
        // Call the original method
        const result = originalMethod.apply(this, args);
        
        // Calculate and log the time taken
        const endTime = Date.now();
        const timeTaken = endTime - startTime;
        console.log(`Method ${propertyName} took ${timeTaken}ms to execute`);
        
        return result;
    };
}

class DataProcessor {
    @Timing
    processLargeArray(data: number[]): number {
        // Simulate some heavy processing
        let sum = 0;
        for (let i = 0; i < data.length; i++) {
            sum += data[i] * 2;
        }
        return sum;
    }
}

// Test timing decorator
const processor = new DataProcessor();
const largeArray = Array.from({length: 1000000}, (_, i) => i);
processor.processLargeArray(largeArray);

// ========================================
// EXAMPLE 2: VALIDATION DECORATOR
// ========================================

// This decorator validates method parameters
function ValidatePositive(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args: any[]) {
        // Check if all arguments are positive numbers
        for (let i = 0; i < args.length; i++) {
            if (typeof args[i] !== 'number' || args[i] <= 0) {
                throw new Error(`Argument ${i + 1} must be a positive number`);
            }
        }
        
        // If validation passes, call the original method
        return originalMethod.apply(this, args);
    };
}

class MathOperations {
    @ValidatePositive
    divide(a: number, b: number): number {
        return a / b;
    }
    
    @ValidatePositive
    sqrt(n: number): number {
        return Math.sqrt(n);
    }
}

// Test validation decorator
const math = new MathOperations();
try {
    console.log(math.divide(10, 2));  // Works fine
    console.log(math.divide(10, -2)); // Will throw error
} catch (error) {
    console.log("Validation error:", error.message);
}

// ========================================
// EXAMPLE 3: CACHING DECORATOR
// ========================================

// This decorator caches method results to avoid repeated calculations
function Cache(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const cache = new Map(); // Store cached results
    
    descriptor.value = function(...args: any[]) {
        // Create a cache key from the arguments
        const cacheKey = JSON.stringify(args);
        
        // Check if result is already cached
        if (cache.has(cacheKey)) {
            console.log(`Cache hit for ${propertyName} with args:`, args);
            return cache.get(cacheKey);
        }
        
        // Calculate result and cache it
        console.log(`Cache miss for ${propertyName}, calculating...`);
        const result = originalMethod.apply(this, args);
        cache.set(cacheKey, result);
        
        return result;
    };
}

class ExpensiveCalculations {
    @Cache
    fibonacci(n: number): number {
        if (n <= 1) return n;
        return this.fibonacci(n - 1) + this.fibonacci(n - 2);
    }
    
    @Cache
    factorial(n: number): number {
        if (n <= 1) return 1;
        return n * this.factorial(n - 1);
    }
}

// Test caching decorator
const calc = new ExpensiveCalculations();
console.log(calc.fibonacci(10)); // First call - will calculate
console.log(calc.fibonacci(10)); // Second call - will use cache

// ========================================
// EXAMPLE 4: MULTIPLE DECORATORS
// ========================================

// You can apply multiple decorators to the same method
// They are applied from bottom to top (closest to the method first)

function UpperCase(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args: any[]) {
        const result = originalMethod.apply(this, args);
        return typeof result === 'string' ? result.toUpperCase() : result;
    };
}

function AddPrefix(prefix: string) {
    return function(target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        
        descriptor.value = function(...args: any[]) {
            const result = originalMethod.apply(this, args);
            return typeof result === 'string' ? prefix + result : result;
        };
    };
}

class TextProcessor {
    @Timing           // Applied third (outermost)
    @UpperCase        // Applied second
    @AddPrefix(">>> ") // Applied first (closest to method)
    formatText(text: string): string {
        return text.trim();
    }
}

// Test multiple decorators
const textProcessor = new TextProcessor();
console.log(textProcessor.formatText("  hello world  "));
// Output: ">>> HELLO WORLD" (and timing info)

// ========================================
// FLOW EXPLANATION
// ========================================

/*
Here's how decorators work step by step:

1. WHEN DECORATORS RUN:
   - Decorators run when the class is DEFINED, not when methods are CALLED
   - They modify the class/method definition itself

2. METHOD DECORATOR FLOW:
   - Original method: function add(a, b) { return a + b; }
   - Decorator wraps it: function(...args) { /* extra code */ originalMethod.apply(this, args); /* more extra code */ }
   - When you call the method, you're actually calling the wrapper function

3. MULTIPLE DECORATORS:
   - Applied from bottom to top (inside out)
   - @A @B @C method() becomes: A(B(C(method)))

4. DECORATOR PARAMETERS:
   - @Decorator(param) is actually: Decorator(param)(target, propertyName, descriptor)
   - The parameter function returns the actual decorator function

5. COMMON PATTERNS:
   - Logging: Add console.log before/after method calls
   - Validation: Check parameters before calling the method
   - Caching: Store and reuse results
   - Timing: Measure execution time
   - Error handling: Wrap methods in try-catch blocks
*/
